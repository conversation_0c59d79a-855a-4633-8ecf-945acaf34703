"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';

interface SidebarContextType {
  isCollapsed: boolean;
  toggleCollapsed: () => void;
  setCollapsed: (collapsed: boolean) => void;
  sidebarWidth: string;
  contentOffset: string;
}

const SidebarContext = createContext<SidebarContextType | null>(null);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Calculate sidebar width and content offset based on collapsed state
  const sidebarWidth = isCollapsed ? '4rem' : '16rem';
  const contentOffset = isCollapsed ? '4rem' : '16rem';

  const toggleCollapsed = useCallback(() => {
    setIsCollapsed(prev => !prev);
  }, []);

  const setCollapsed = useCallback((collapsed: boolean) => {
    setIsCollapsed(collapsed);
  }, []);

  // Update CSS custom properties when sidebar state changes
  useEffect(() => {
    document.documentElement.style.setProperty('--sidebar-width', sidebarWidth);
    document.documentElement.style.setProperty('--content-offset', contentOffset);
  }, [sidebarWidth, contentOffset]);

  const value: SidebarContextType = {
    isCollapsed,
    toggleCollapsed,
    setCollapsed,
    sidebarWidth,
    contentOffset
  };

  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar(): SidebarContextType {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}