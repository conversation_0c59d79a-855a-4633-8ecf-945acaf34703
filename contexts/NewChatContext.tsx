"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';

interface NewChatContextType {
  showNewChatButton: boolean;
  setShowNewChatButton: (show: boolean) => void;
  onNewChat: (() => void) | null;
  setOnNewChat: (callback: (() => void) | null) => void;
}

const NewChatContext = createContext<NewChatContextType | undefined>(undefined);

export function NewChatProvider({ children }: { children: React.ReactNode }) {
  const [showNewChatButton, setShowNewChatButton] = useState(false);
  const [onNewChat, setOnNewChat] = useState<(() => void) | null>(null);

  const value = {
    showNewChatButton,
    setShowNewChatButton,
    onNewChat,
    setOnNewChat: useCallback((callback: (() => void) | null) => {
      setOnNewChat(() => callback);
    }, [])
  };

  return (
    <NewChatContext.Provider value={value}>
      {children}
    </NewChatContext.Provider>
  );
}

export function useNewChat() {
  const context = useContext(NewChatContext);
  if (context === undefined) {
    throw new Error('useNewChat must be used within a NewChatProvider');
  }
  return context;
}
