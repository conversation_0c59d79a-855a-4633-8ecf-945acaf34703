"use client";

import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'danger' | 'black';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

export default function Button({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  icon,
  fullWidth = false,
  className = '',
  disabled,
  ...props
}: ButtonProps) {
  
  const baseStyles = "inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black";
  
  const variantStyles = {
    primary: "bg-[#FD2D55] text-white hover:bg-[#FD2D55]/90",
    secondary: "bg-gray-200 text-black hover:bg-gray-300",
    outline: "bg-transparent text-black border border-gray-300 hover:bg-gray-100",
    danger: "bg-red-600 text-white hover:bg-red-700",
    black: "bg-black text-white hover:bg-gray-900"
  };
  
  const sizeStyles = {
    sm: "text-xs px-3 py-1.5 rounded-3xl",
    md: "text-sm px-4 py-2 rounded-3xl",
    lg: "text-base px-5 py-2.5 rounded-3xl"
  };
  
  const disabledStyles = "opacity-50 cursor-not-allowed";
  const loadingStyles = "cursor-wait";
  const widthStyles = fullWidth ? "w-full" : "";
  
  const buttonStyles = `
    ${baseStyles} 
    ${variantStyles[variant]} 
    ${sizeStyles[size]} 
    ${disabled || isLoading ? disabledStyles : ''} 
    ${isLoading ? loadingStyles : ''} 
    ${widthStyles}
    ${className}
  `;

  return (
    <button 
      className={buttonStyles}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {!isLoading && icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
}