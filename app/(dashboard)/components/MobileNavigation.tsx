"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { UserButton, useUser } from '@clerk/nextjs';
import { 
  X, 
  Plus, 
  Library, 
  Settings,
} from 'lucide-react';
import { useMobileNavigation } from '@/contexts/MobileNavigationContext';

interface MobileNavigationProps {
  className?: string;
}

export default function MobileNavigation({ className = '' }: MobileNavigationProps) {
  const { isMenuOpen, closeMenu } = useMobileNavigation();
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();
  const { user } = useUser();
  
  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(path + '/');
  };

  // Fetch credits functionality
  const fetchCredits = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        setCredits(data.credits);
      }
    } catch (error) {
      console.error('[MobileNavigation] Error fetching credits:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      fetchCredits();

      const handleCreditUpdate = () => {
        fetchCredits();
      };
      
      window.addEventListener('creditUpdated', handleCreditUpdate);
      
      return () => {
        window.removeEventListener('creditUpdated', handleCreditUpdate);
      };
    }
  }, [isMenuOpen, fetchCredits]);

  const navItems = [
    { 
      label: 'Create', 
      href: '/create', 
      icon: <Plus size={24} />,
      description: 'Generate AI content'
    },
    { 
      label: 'Library', 
      href: '/library', 
      icon: <Library size={24} />,
      description: 'View your images'
    },
    { 
      label: 'Settings', 
      href: '/settings', 
      icon: <Settings size={24} />,
      description: 'Account & billing'
    },
  ];

  return (
    <>
      {/* Backdrop */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={closeMenu}
          aria-hidden="true"
        />
      )}
      
      {/* Navigation Drawer */}
      <div className={`
        fixed top-0 left-0 h-full w-80 bg-white z-50 transform transition-transform duration-300 ease-in-out lg:hidden shadow-xl
        ${isMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        ${className}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold" style={{ color: '#FD2D55' }}>
              MARKET-ME
            </h2>
            <button
              onClick={closeMenu}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors touch-manipulation"
              aria-label="Close navigation menu"
            >
              <X size={24} className="text-gray-600" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <UserButton 
                afterSignOutUrl="/" 
                appearance={{
                  elements: {
                    avatarBox: "w-12 h-12",
                  }
                }}
              />
              <div className="flex-1 min-w-0">
                <div className="text-base font-medium text-gray-900 truncate">
                  {user?.firstName && user?.lastName 
                    ? `${user.firstName} ${user.lastName}`
                    : user?.fullName || user?.firstName || 'User'
                  }
                </div>
                <div className="text-sm text-gray-500 truncate">
                  {user?.primaryEmailAddress?.emailAddress}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <nav className="flex-1 p-6">
            <div className="space-y-2">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={closeMenu}
                  className={`
                    flex items-center gap-4 px-4 py-3 rounded-xl transition-all text-base font-medium touch-manipulation
                    ${isActive(item.href) 
                      ? 'bg-[#FD2D55] text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  <div className="flex-1">
                    <div>{item.label}</div>
                    <div className={`text-sm ${
                      isActive(item.href) ? 'text-white/80' : 'text-gray-500'
                    }`}>
                      {item.description}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </nav>

          {/* Credits Section */}
          <div className="p-6 border-t border-gray-200">
            <Link 
              href="/settings" 
              onClick={closeMenu}
              className="flex items-center gap-4 px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors touch-manipulation"
            >
              <span className="text-2xl flex-shrink-0">💰</span>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  {loading ? (
                    <span className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></span>
                  ) : (
                    <span className="font-semibold text-lg">{credits}</span>
                  )}
                  <span className="text-gray-500">Credits</span>
                </div>
                <div className="text-sm text-gray-500">
                  Tap to manage billing
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
