import React from 'react';

interface TabHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  rightContent?: React.ReactNode;
  alignment?: 'left' | 'center';
}

export default function TabHeader({ 
  title, 
  subtitle,
  description,
  rightContent,
  alignment = 'left'
}: TabHeaderProps) {
  return (
    <div className={`flex items-center mb-4 w-full ${alignment === 'center' ? 'justify-center' : 'justify-start'}`}>
      <div className={`${alignment === 'center' ? 'text-center' : 'text-left'}`}>
        <h2 className="text-2xl font-bold text-black">{title}</h2>
        {subtitle && (
          <p className="text-gray-600 text-sm mt-1">{subtitle}</p>
        )}
        {description && (
          <p className="text-gray-600 mt-3 text-sm">{description}</p>
        )}
      </div>
      {rightContent && (
        <div className="absolute right-4">
          {rightContent}
        </div>
      )}
    </div>
  );
}