"use client";

import React from 'react';
import { useSidebar } from '@/hooks/useSidebar';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
}

export default function ResponsiveContainer({ children, className = '' }: ResponsiveContainerProps) {
  const { sidebarWidth } = useSidebar();

  return (
    <div 
      className={`responsive-container h-full ${className}`}
      style={{
        marginLeft: `var(--sidebar-width, ${sidebarWidth})`,
        width: `calc(100vw - var(--sidebar-width, ${sidebarWidth}))`,
        transition: 'all 300ms ease-in-out'
      }}
    >
      <div className="content-wrapper w-full h-full flex justify-center">
        <div className="w-full max-w-full px-4 min-h-0">
          {children}
        </div>
      </div>
    </div>
  );
}