/**
 * useStreamingAgent Hook
 * 
 * Purpose: Manages OpenAI streaming responses with EventSource (SSE)
 * Performance Notes: Optimized state updates to prevent unnecessary re-renders
 * Dependencies: StreamingState for type safety, EventSource for SSE
 */

"use client";

import { useCallback, useRef, useState } from 'react';
import { StreamingState } from '../components/StreamingMessage';
import { ToolCallState } from '../components/ToolCallProgress';
import { UserContext } from '../types/conversation';

interface StreamingCallbacks {
  onComplete?: (response: any) => void;
  onError?: (error: string) => void;
  onImagesReady?: (images: any[], toolResults: any[]) => void;
}

export function useStreamingAgent(userContext?: UserContext) {
  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    accumulatedText: '',
    currentToolCalls: [],
    isComplete: false,
    parallelMode: false,
    totalCalls: 0,
    completedCalls: 0,
    overallProgress: 0
  });

  const eventSourceRef = useRef<EventSource | null>(null);
  const isStreamingRef = useRef(false);
  const callbacksRef = useRef<StreamingCallbacks | null>(null);

  // Filtered client-side logging function
  const logClientEvent = useCallback((data: any) => {
    switch (data.type) {
      case 'connected':
      case 'stream_end':
        console.log(`[Client] ${data.type.toUpperCase()}: Stream lifecycle`);
        break;
        
      case 'tool_call_start':
        console.log(`[Client] TOOL_START: ${data.toolName} (${data.toolId})`);
        break;
        
      case 'tool_call_complete':
        console.log(`[Client] TOOL_COMPLETE: ${data.toolName} -> ${data.state}`, {
          success: data.result?.success,
          hasImage: !!data.result?.image,
          hasError: !!data.result?.error
        });
        break;
        
      case 'message_complete':
        console.log(`[Client] MESSAGE_COMPLETE: Final response received`, {
          messageLength: data.message?.length || 0,
          toolResults: data.toolResults?.length || 0,
          images: data.generatedImages?.length || 0
        });
        break;
        
      case 'error':
        console.error(`[Client] ERROR: ${data.error} (${data.type})`);
        break;
        
      // Skip verbose events
      case 'text_delta':
      case 'tool_call_progress':
        // Only log these occasionally to avoid spam
        break;
        
      default:
        console.log(`[Client] ${data.type?.toUpperCase() || 'UNKNOWN'}: Event received`);
    }
  }, []);

  // Reset streaming state
  const resetStreamingState = useCallback(() => {
    setStreamingState({
      isStreaming: false,
      accumulatedText: '',
      currentToolCalls: [],
      isComplete: false
    });
  }, []);

  // Clean up EventSource connection
  const cleanupEventSource = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    isStreamingRef.current = false;
  }, []);

  // Update tool call state
  const updateToolCall = useCallback((toolId: string, updates: Partial<{
    state: ToolCallState;
    message?: string;
    progress?: number;
    streamingText?: string;
    error?: string;
  }>) => {
    setStreamingState(prev => ({
      ...prev,
      currentToolCalls: prev.currentToolCalls.map(toolCall =>
        toolCall.id === toolId
          ? { ...toolCall, ...updates }
          : toolCall
      )
    }));
  }, []);

  // Add new tool call
  const addToolCall = useCallback((toolId: string, toolName: string) => {
    setStreamingState(prev => ({
      ...prev,
      currentToolCalls: [
        ...prev.currentToolCalls,
        {
          id: toolId,
          toolName,
          state: 'pending' as ToolCallState,
        }
      ]
    }));
  }, []);

  // Stream message with Server-Sent Events
  const streamMessage = useCallback(async (
    message: string,
    images?: string[],
    conversationHistory?: any[],
    callbacks?: StreamingCallbacks
  ) => {
    if (isStreamingRef.current) {
      console.warn('[useStreamingAgent] Already streaming, ignoring new request');
      return;
    }

    try {
      isStreamingRef.current = true;
      callbacksRef.current = callbacks || null;

      // Reset state for new stream
      resetStreamingState();

      // Set initial streaming state
      setStreamingState(prev => ({
        ...prev,
        isStreaming: true,
        isComplete: false
      }));

      // Convert blob URLs to base64 data URLs before sending to API
      let processedImages = images;
      if (images && images.length > 0) {
        console.log('[useStreamingAgent] Converting blob URLs to base64...');
        const { ImageService } = await import('@/lib/services/image-service');
        processedImages = await ImageService.processImageUrls(images);
        console.log('[useStreamingAgent] Image conversion completed:', {
          originalCount: images.length,
          processedCount: processedImages.length
        });
      }

      // Convert blob URLs in conversation history as well
      let processedConversationHistory = conversationHistory;
      if (conversationHistory && conversationHistory.length > 0) {
        console.log('[useStreamingAgent] Converting blob URLs in conversation history...');
        const { ImageService } = await import('@/lib/services/image-service');
        processedConversationHistory = await ImageService.convertBlobUrlsInHistory(conversationHistory);
        console.log('[useStreamingAgent] Conversation history conversion completed:', {
          originalMessages: conversationHistory.length,
          processedMessages: processedConversationHistory.length
        });
      }

      // Use fetch with streaming since EventSource doesn't support POST with body
      const response = await fetch('/api/ai-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({
          message,
          images: processedImages,
          conversationHistory: processedConversationHistory,
          userContext,
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      let buffer = '';
      let completedResponse: any = null;

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          
          // Keep the last incomplete line in buffer
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') continue;
            
            if (line.startsWith('data: ')) {
              try {
                const dataStr = line.slice(6);
                if (dataStr.trim() === '') continue;
                
                const data = JSON.parse(dataStr);
                
                // Filtered client-side logging
                logClientEvent(data);
                
                // Handle different event types and capture final response
                if (data.type === 'message_complete') {
                  completedResponse = data;
                }
                
                await handleStreamEvent(data, { updateToolCall, addToolCall });
              } catch (parseError) {
                console.warn('[useStreamingAgent] Failed to parse SSE data:', line, parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // Mark as complete and get final state
      const finalState = await new Promise<StreamingState>((resolve) => {
        setStreamingState(prev => {
          const newState = {
            ...prev,
            isStreaming: false,
            isComplete: true
          };
          resolve(newState);
          return newState;
        });
      });

      // Call completion callback with final response data
      callbacks?.onComplete?.(completedResponse || finalState);

    } catch (error) {
      console.error('[useStreamingAgent] Streaming error:', error);
      
      setStreamingState(prev => ({
        ...prev,
        isStreaming: false,
        isComplete: true
      }));

      const errorMessage = error instanceof Error ? error.message : 'Unknown streaming error';
      callbacks?.onError?.(errorMessage);
    } finally {
      isStreamingRef.current = false;
      cleanupEventSource();
    }
  }, [userContext, resetStreamingState, updateToolCall, addToolCall, cleanupEventSource, logClientEvent]);

  // Handle individual stream events
  const handleStreamEvent = async (
    eventData: any,
    handlers: {
      updateToolCall: (id: string, updates: any) => void;
      addToolCall: (id: string, name: string) => void;
    }
  ) => {
    const { updateToolCall, addToolCall } = handlers;

    switch (eventData.type || 'text_delta') {
      case 'connected':
        console.log('[useStreamingAgent] Stream connected');
        break;

      case 'text_delta':
        setStreamingState(prev => ({
          ...prev,
          accumulatedText: eventData.accumulated || prev.accumulatedText + (eventData.content || '')
        }));
        break;

      case 'tool_call_start':
        addToolCall(eventData.toolId, eventData.toolName);
        updateToolCall(eventData.toolId, {
          state: 'running' as ToolCallState
        });
        
        // Update parallel tracking
        setStreamingState(prev => {
          const newTotalCalls = (prev.totalCalls ?? 0) + 1;
          const isParallel = newTotalCalls > 1;
          return {
            ...prev,
            parallelMode: isParallel,
            totalCalls: newTotalCalls,
            overallProgress: (prev.completedCalls ?? 0) / newTotalCalls * 100
          };
        });
        break;

      case 'tool_call_progress':
        updateToolCall(eventData.toolId, {
          state: 'streaming' as ToolCallState,
          streamingText: eventData.progress?.arguments,
          progress: eventData.progress?.percentage
        });
        break;

      case 'tool_call_complete':
        updateToolCall(eventData.toolId, {
          state: eventData.state as ToolCallState,
          message: eventData.result?.userMessage || (eventData.state === 'success' ? 'Completed successfully' : 'Failed'),
          error: eventData.result?.error
        });

        // Update parallel completion tracking
        setStreamingState(prev => {
          const newCompletedCalls = (prev.completedCalls ?? 0) + 1;
          const totalCalls = prev.totalCalls ?? 1;
          return {
            ...prev,
            completedCalls: newCompletedCalls,
            overallProgress: (newCompletedCalls / totalCalls) * 100
          };
        });
        break;

      case 'images_ready':
        console.log('[useStreamingAgent] Images ready for immediate display:', eventData.images?.length);
        // Call the callback to immediately display images
        if (callbacksRef.current?.onImagesReady && eventData.images) {
          callbacksRef.current.onImagesReady(eventData.images, eventData.toolResults);
        }
        break;

      case 'message_complete':
        setStreamingState(prev => ({
          ...prev,
          isStreaming: false,
          isComplete: true,
          accumulatedText: eventData.message || prev.accumulatedText
        }));
        break;

      case 'error':
        console.error('[useStreamingAgent] Stream error:', eventData.error);
        setStreamingState(prev => ({
          ...prev,
          isStreaming: false,
          isComplete: true
        }));
        break;

      case 'stream_end':
        console.log('[useStreamingAgent] Stream ended');
        break;

      default:
        console.warn('[useStreamingAgent] Unknown event type:', eventData);
    }
  };

  // Stop streaming
  const stopStreaming = useCallback(() => {
    cleanupEventSource();
    setStreamingState(prev => ({
      ...prev,
      isStreaming: false,
      isComplete: true
    }));
  }, [cleanupEventSource]);

  // Retry tool call
  const retryToolCall = useCallback((toolId: string) => {
    updateToolCall(toolId, {
      state: 'pending' as ToolCallState,
      error: undefined
    });
    // TODO: Implement actual retry logic
    console.log('[useStreamingAgent] Retry tool call:', toolId);
  }, [updateToolCall]);

  return {
    streamingState,
    streamMessage,
    stopStreaming,
    retryToolCall,
    isStreaming: isStreamingRef.current,
    resetStreamingState
  };
}
