"use client";

import { useState, useCallback } from 'react';
import { ImageGenerationParams } from '../types/agent';
import { Message, UserContext, ContentItem } from '../types/conversation';
import { ImageService } from '@/lib/services/image-service';

interface UseAIAgentProps {
  onMessage: (message: Omit<Message, 'id' | 'timestamp'>) => string;
  onTypingChange: (isTyping: boolean) => void;
  userContext?: UserContext;
  conversationHistory: any[];
}

export function useAIAgent({ onMessage, onTypingChange, userContext, conversationHistory }: UseAIAgentProps) {
  const [isProcessing, setIsProcessing] = useState(false);


  // Send message to AI agent
  const sendMessage = useCallback(async (userMessage: string, images?: string[]) => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      onTypingChange(true);

      // Convert blob URLs to base64 data URLs for OpenAI API
      let processedImages: string[] | undefined;
      if (images && images.length > 0) {
        processedImages = await ImageService.processImageUrls(images);
      }

      // Add diagnostic logging to confirm the storage issue
      console.log('[useAIAgent] Before storing message in conversation history:');
      console.log('[useAIAgent] Original images:', images?.map(img => ({
        url: img.substring(0, 50) + '...',
        isBlob: img.startsWith('blob:'),
        isData: img.startsWith('data:')
      })));
      console.log('[useAIAgent] Processed images:', processedImages?.map(img => ({
        url: img.substring(0, 50) + '...',
        isBlob: img.startsWith('blob:'),
        isData: img.startsWith('data:')
      })));

      // Add user message with OpenAI format content
      let messageContent: string | ContentItem[];
      
      if (processedImages && processedImages.length > 0) {
        // Create OpenAI format with both text and images
        const contentItems: ContentItem[] = [];
        
        if (userMessage.trim()) {
          contentItems.push({ type: 'text', text: userMessage });
        }
        
        processedImages.forEach(imageUrl => {
          contentItems.push({ 
            type: 'image_url', 
            image_url: { url: imageUrl } 
          });
        });
        
        messageContent = contentItems;
      } else {
        // Text only message
        messageContent = userMessage;
      }
      
      const userMessageId = onMessage({
        type: 'user',
        content: messageContent
      });

      // Send to AI agent API (with base64 data URLs)
      const response = await fetch('/api/ai-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          images: processedImages,
          conversationHistory,
          userContext
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get response from AI agent');
      }

      const data = await response.json();
      
      // Add agent response (without generated images - those go to main preview via structured output)
      const agentMessageId = onMessage({
        type: 'agent',
        content: data.message,
        functionCall: data.functionCall,
        functionResult: data.functionResult
        // Note: Generated images are handled via structured output callback, not chat messages
      });

      return { userMessageId, agentMessageId, data };

    } catch (error) {
      console.error('[useAIAgent] Error sending message:', error);
      
      // Add error message
      onMessage({
        type: 'agent',
        content: "I'm sorry, I'm having trouble responding right now. Please try again in a moment."
      });

      throw error;
    } finally {
      setIsProcessing(false);
      onTypingChange(false);
    }
  }, [isProcessing, onMessage, onTypingChange, conversationHistory, userContext]);

  // Send initial welcome message
  const sendWelcomeMessage = useCallback(async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      onTypingChange(true);

      const response = await fetch('/api/ai-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: '',
          isWelcome: true,
          userContext
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get welcome message');
      }

      const data = await response.json();
      
      onMessage({
        type: 'agent',
        content: data.message
      });

    } catch (error) {
      console.error('[useAIAgent] Error sending welcome message:', error);
      
      // Fallback welcome message
      const fallbackMessage = userContext?.name 
        ? `Hi ${userContext.name}! I'm Yaya, your AI marketing agent. I'm here to help you create stunning marketing content for your fashion brand. You have ${userContext.credits} credits available. What would you like to create today?`
        : "Hi! I'm Yaya, your AI marketing agent. I'm here to help you create stunning marketing content for your fashion brand. What would you like to create today?";
      
      onMessage({
        type: 'agent',
        content: fallbackMessage
      });
    } finally {
      setIsProcessing(false);
      onTypingChange(false);
    }
  }, [isProcessing, onMessage, onTypingChange, userContext]);

  // Generate images using AI agent
  const generateImages = useCallback(async (params: ImageGenerationParams) => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      onTypingChange(true);

      const response = await fetch('/api/ai-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          functionCall: {
            name: 'generateImages',
            arguments: params
          },
          userContext
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate images');
      }

      const data = await response.json();
      
      onMessage({
        type: 'agent',
        content: data.message,
        functionResult: data.functionResult
        // Note: Generated images are handled via structured output callback, not chat messages
      });

      return data;

    } catch (error) {
      console.error('[useAIAgent] Error generating images:', error);
      
      onMessage({
        type: 'agent',
        content: "I had trouble generating those images. Let me know if you'd like to try again with different settings."
      });

      throw error;
    } finally {
      setIsProcessing(false);
      onTypingChange(false);
    }
  }, [isProcessing, onMessage, onTypingChange, userContext]);

  // Get user context (credits, subscription, etc.)
  const fetchUserContext = useCallback(async () => {
    try {
      const response = await fetch('/api/user-context');
      
      if (!response.ok) {
        throw new Error('Failed to fetch user context');
      }

      const data = await response.json();
      return data;

    } catch (error) {
      console.error('[useAIAgent] Error fetching user context:', error);
      return null;
    }
  }, []);

  return {
    isProcessing,
    sendMessage,
    sendWelcomeMessage,
    generateImages,
    fetchUserContext
  };
}
