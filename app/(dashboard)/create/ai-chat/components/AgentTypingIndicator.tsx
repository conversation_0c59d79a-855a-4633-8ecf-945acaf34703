"use client";

interface AgentTypingIndicatorProps {
  isVisible: boolean;
}

export default function AgentTypingIndicator({ isVisible }: AgentTypingIndicatorProps) {
  if (!isVisible) return null;

  return (
    <div className="flex gap-3 mb-6 justify-start">
      {/* Avatar */}
      <div className="flex-shrink-0 w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center mt-1">
      </div>

      {/* Typing Animation */}
      <div className="flex items-center space-x-1 mt-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
      </div>
    </div>
  );
}