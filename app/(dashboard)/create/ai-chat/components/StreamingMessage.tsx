/**
 * StreamingMessage Component
 * 
 * Purpose: Displays real-time streaming responses without re-rendering other messages
 * Performance Notes: Uses useRef for text accumulation to avoid state-based re-renders
 * Dependencies: ToolCallProgress for tool call visualization
 */

"use client";

import React, { memo, useMemo, useRef, useEffect, useState } from 'react';
import { Bot } from 'lucide-react';
import dynamic from 'next/dynamic';
import ToolCallProgress, { ToolCallState } from './ToolCallProgress';

// Dynamically import MarkdownRenderer
const MarkdownRenderer = dynamic(() => import('./MarkdownRenderer'), { 
  ssr: false,
  loading: () => <div className="animate-pulse">Loading...</div>
});

export interface StreamingState {
  isStreaming: boolean;
  accumulatedText: string;
  currentToolCalls: Array<{
    id: string;
    toolName: string;
    state: ToolCallState;
    message?: string;
    progress?: number;
    streamingText?: string;
    error?: string;
  }>;
  isComplete: boolean;
  parallelMode?: boolean;
  totalCalls?: number;
  completedCalls?: number;
  overallProgress?: number;
}

interface StreamingMessageProps {
  streamingState: StreamingState;
  onRetryToolCall?: (toolId: string) => void;
}

/**
 * Memoized tool call list to prevent re-rendering when text updates
 */
const ToolCallsList = memo<{
  toolCalls: StreamingState['currentToolCalls'];
  onRetry?: (toolId: string) => void;
}>(({ toolCalls, onRetry }) => {
  return (
    <>
      {toolCalls.map((toolCall) => (
        <ToolCallProgress
          key={toolCall.id}
          toolName={toolCall.toolName}
          state={toolCall.state}
          message={toolCall.message}
          progress={toolCall.progress}
          streamingText={toolCall.streamingText}
          error={toolCall.error}
          isVisible={true}
          onRetry={onRetry ? () => onRetry(toolCall.id) : undefined}
        />
      ))}
    </>
  );
});

ToolCallsList.displayName = 'ToolCallsList';

/**
 * Optimized streaming text display with throttled markdown rendering
 */
const StreamingTextDisplay = memo<{
  text: string;
  isComplete: boolean;
}>(({ text, isComplete }) => {
  const [displayText, setDisplayText] = useState(text);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(0);

  useEffect(() => {
    if (isComplete) {
      // When complete, immediately show final text
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      setDisplayText(text);
      return;
    }

    // During streaming, throttle updates to reduce excessive re-renders
    const now = Date.now();
    const timeSinceLastUpdate = now - lastUpdateRef.current;
    const THROTTLE_MS = 150; // Update every 150ms during streaming

    if (timeSinceLastUpdate >= THROTTLE_MS) {
      // Enough time has passed, update immediately
      lastUpdateRef.current = now;
      setDisplayText(text);
    } else {
      // Too soon, schedule an update
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        lastUpdateRef.current = Date.now();
        setDisplayText(text);
        timeoutRef.current = null;
      }, THROTTLE_MS - timeSinceLastUpdate);
    }

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [text, isComplete]);

  if (displayText.trim()) {
    return (
      <MarkdownRenderer
        content={displayText}
        isStreamFinished={isComplete}
      />
    );
  }

  // Fallback for empty content
  return (
    <div className="whitespace-pre-wrap text-[15px] leading-relaxed min-h-[1.5rem]" />
  );
});

StreamingTextDisplay.displayName = 'StreamingTextDisplay';

/**
 * Main StreamingMessage component
 */
const StreamingMessage = memo<StreamingMessageProps>(({ 
  streamingState, 
  onRetryToolCall 
}) => {
  const { isStreaming, accumulatedText, currentToolCalls, isComplete } = streamingState;

  // Memoize visibility check - hide when complete to avoid duplicate with permanent message
  const isVisible = useMemo(() => {
    // Show only when actively streaming or has active tool calls
    // Hide when complete to prevent duplication with permanent MessageBubble
    if (isComplete && !isStreaming && currentToolCalls.every(call => call.state === 'success' || call.state === 'error')) {
      return false;
    }
    return isStreaming || accumulatedText.trim().length > 0 || currentToolCalls.length > 0;
  }, [isStreaming, accumulatedText, currentToolCalls, isComplete]);

  // Memoize animation classes with fade out when complete
  const containerClasses = useMemo(() => {
    const baseClasses = "flex gap-3 mb-6 justify-start transition-opacity duration-500";
    if (!isVisible) {
      return `${baseClasses} opacity-0 pointer-events-none`;
    }
    // Fade out when complete but before hiding
    if (isComplete && !isStreaming) {
      return `${baseClasses} opacity-30`;
    }
    return `${baseClasses} opacity-100`;
  }, [isVisible, isComplete, isStreaming]);

  // Memoized cursor animation
  const showCursor = useMemo(() => 
    isStreaming && !isComplete,
    [isStreaming, isComplete]
  );

  // Don't render if nothing to show
  if (!isVisible) {
    return null;
  }

  return (
    <div className={containerClasses}>
      {/* Avatar */}
      <div className="flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center mt-1">
        <Bot size={14} className="text-gray-600" />
      </div>

      {/* Content */}
      <div className="max-w-[85%]">
        {/* Tool Calls */}
        {currentToolCalls.length > 0 && (
          <div className="mb-4">
            <ToolCallsList 
              toolCalls={currentToolCalls} 
              onRetry={onRetryToolCall}
            />
          </div>
        )}

        {/* Streaming Text */}
        {(accumulatedText.trim().length > 0 || isStreaming) && (
          <div className="text-black">
            <div className="relative">
              <StreamingTextDisplay 
                text={accumulatedText}
                isComplete={isComplete}
              />
              
              {/* Streaming cursor */}
              {showCursor && (
                <span 
                  className="inline-block w-2 h-5 bg-gray-400 ml-1 animate-pulse"
                  style={{ animation: 'blink 1s infinite' }}
                />
              )}
            </div>
          </div>
        )}
      </div>
      
      {/* CSS for cursor animation */}
      <style jsx>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </div>
  );
});

StreamingMessage.displayName = 'StreamingMessage';

export default StreamingMessage;
