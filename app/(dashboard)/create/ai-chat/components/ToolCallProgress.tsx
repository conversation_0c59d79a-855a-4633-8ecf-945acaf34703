/**
 * ToolCallProgress Component
 * 
 * Purpose: Displays real-time progress for AI agent tool calls
 * Performance Notes: Uses React.memo for optimization, minimal re-renders
 * Dependencies: Tailwind CSS for styling, Lucide React for icons
 */

"use client";

import React, { memo, useMemo } from 'react';
import { 
  Image, 
  Brain, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Clock,
  Search,
  Camera,
  Save
} from 'lucide-react';

export type ToolCallState = 'pending' | 'running' | 'success' | 'error' | 'streaming';

export interface ToolCallProgressProps {
  toolName: string;
  state: ToolCallState;
  message?: string;
  progress?: number; // 0-100 for progress bar
  streamingText?: string; // For showing real-time argument building
  error?: string;
  isVisible: boolean;
  onRetry?: () => void;
}

// Tool-specific configurations
const TOOL_CONFIGS = {
  generateImages: {
    label: 'Generating Images',
    icon: Image,
    color: 'blue',
    description: 'Creating professional marketing images...',
    successMessage: 'Images generated successfully!',
    streamingLabel: 'Analyzing requirements'
  },
  generateSingleImage: {
    label: 'Generating Image',
    icon: Image,
    color: 'blue',
    description: 'Creating marketing image...',
    successMessage: 'Image generated successfully!',
    streamingLabel: 'Processing image prompt'
  },
  saveMemory: {
    label: 'Saving Memory',
    icon: Save,
    color: 'green',
    description: 'Storing your preferences...',
    successMessage: 'Preferences saved!',
    streamingLabel: 'Processing memory'
  },
  searchWeb: {
    label: 'Searching Web',
    icon: Search,
    color: 'purple',
    description: 'Finding inspiration and trends...',
    successMessage: 'Search completed!',
    streamingLabel: 'Searching trends'
  },
  analyzeImage: {
    label: 'Analyzing Image',
    icon: Camera,
    color: 'orange',
    description: 'Understanding your product...',
    successMessage: 'Analysis complete!',
    streamingLabel: 'Analyzing content'
  },
  default: {
    label: 'Processing',
    icon: Brain,
    color: 'gray',
    description: 'Working on your request...',
    successMessage: 'Task completed!',
    streamingLabel: 'Processing'
  }
} as const;

/**
 * Get color classes for different states and tool types
 */
const getColorClasses = (color: string, state: ToolCallState) => {
  const baseColors = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'text-blue-600',
      accent: 'bg-blue-500',
      text: 'text-blue-800'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200', 
      icon: 'text-green-600',
      accent: 'bg-green-500',
      text: 'text-green-800'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'text-purple-600', 
      accent: 'bg-purple-500',
      text: 'text-purple-800'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      icon: 'text-orange-600',
      accent: 'bg-orange-500', 
      text: 'text-orange-800'
    },
    gray: {
      bg: 'bg-gray-50',
      border: 'border-gray-200',
      icon: 'text-gray-600',
      accent: 'bg-gray-500',
      text: 'text-gray-800'
    }
  };

  const stateOverrides = {
    success: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      icon: 'text-green-600',
      accent: 'bg-green-500',
      text: 'text-green-800'
    },
    error: {
      bg: 'bg-red-50', 
      border: 'border-red-200',
      icon: 'text-red-600',
      accent: 'bg-red-500',
      text: 'text-red-800'
    }
  };

  if (state === 'success' || state === 'error') {
    return stateOverrides[state];
  }

  return baseColors[color as keyof typeof baseColors] || baseColors.gray;
};

/**
 * Progress bar component with smooth animations
 */
const ProgressBar = memo<{ progress?: number; color: string; state: ToolCallState }>(
  ({ progress, color, state }) => {
    const colors = getColorClasses(color, state);
    
    // Indeterminate animation for streaming/running states
    if (state === 'streaming' || state === 'running') {
      return (
        <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className={`h-full ${colors.accent} animate-pulse transition-all duration-300`}
            style={{
              background: `linear-gradient(90deg, transparent, currentColor, transparent)`,
              animation: 'shimmer 2s infinite linear'
            }}
          />
        </div>
      );
    }

    // Determinate progress bar
    if (typeof progress === 'number') {
      return (
        <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className={`h-full ${colors.accent} transition-all duration-500 ease-out`}
            style={{ width: `${Math.min(Math.max(progress, 0), 100)}%` }}
          />
        </div>
      );
    }

    return null;
  }
);

ProgressBar.displayName = 'ProgressBar';

/**
 * State icon with animations
 */
const StateIcon = memo<{ state: ToolCallState; toolConfig: any; colors: any }>(
  ({ state, toolConfig, colors }) => {
    const iconProps = {
      size: 16,
      className: `${colors.icon} transition-colors duration-200`
    };

    switch (state) {
      case 'success':
        return <CheckCircle {...iconProps} className="text-green-600" />;
      
      case 'error':
        return <XCircle {...iconProps} className="text-red-600" />;
      
      case 'running':
      case 'streaming':
        return <Loader2 {...iconProps} className="animate-spin" />;
      
      case 'pending':
        return <Clock {...iconProps} className="text-gray-500" />;
      
      default:
        const ToolIcon = toolConfig.icon;
        return <ToolIcon {...iconProps} />;
    }
  }
);

StateIcon.displayName = 'StateIcon';

/**
 * Main ToolCallProgress component
 */
export const ToolCallProgress = memo<ToolCallProgressProps>(({
  toolName,
  state,
  message,
  progress,
  streamingText,
  error,
  isVisible,
  onRetry
}) => {
  // Get tool configuration
  const toolConfig = useMemo(() => {
    return TOOL_CONFIGS[toolName as keyof typeof TOOL_CONFIGS] || TOOL_CONFIGS.default;
  }, [toolName]);

  // Get color scheme
  const colors = useMemo(() => {
    return getColorClasses(toolConfig.color, state);
  }, [toolConfig.color, state]);

  // Display message based on state and props (clean, user-friendly text only)
  const displayMessage = useMemo(() => {
    if (error && state === 'error') return error;
    if (message) return message;
    
    switch (state) {
      case 'pending':
        return `Preparing to ${toolConfig.label.toLowerCase()}...`;
      case 'running':
        return toolConfig.description;
      case 'streaming':
        // Always show clean description for streaming, detailed content goes in the box below
        return toolConfig.description;
      case 'success':
        return toolConfig.successMessage;
      case 'error':
        return `Failed to ${toolConfig.label.toLowerCase()}`;
      default:
        return toolConfig.description;
    }
  }, [state, message, error, toolConfig]);

  // Animation classes
  const animationClasses = useMemo(() => {
    const base = "transition-all duration-300 ease-in-out";
    if (!isVisible) return `${base} opacity-0 scale-95 pointer-events-none`;
    return `${base} opacity-100 scale-100`;
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className={`flex gap-3 mb-6 justify-start ${animationClasses}`}>
      {/* Progress Card - No icon, using existing bot avatar */}
      <div className={`
        w-full min-w-[300px] rounded-lg border-2 p-4 shadow-sm
        ${colors.bg} ${colors.border}
        transform hover:scale-[1.02] transition-transform duration-200
      `}>
        {/* Header */}
        <div className="flex items-center gap-3 mb-3">
          <StateIcon state={state} toolConfig={toolConfig} colors={colors} />
          <div className="flex-1 min-w-0">
            <h4 className={`font-medium text-sm ${colors.text}`}>
              {toolConfig.label}
            </h4>
            <p className="text-xs text-gray-600 truncate">
              {displayMessage}
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-3">
          <ProgressBar progress={progress} color={toolConfig.color} state={state} />
        </div>

        {/* Streaming Text Display */}
        {streamingText && state === 'streaming' && (
          <div className="mb-3 p-3 bg-white bg-opacity-70 rounded-md border border-gray-200">
            <div className="text-xs font-medium text-gray-600 mb-1">Processing:</div>
            <div className="text-sm text-gray-800 max-h-20 overflow-y-auto leading-relaxed">
              {/* Extract and format the actual image generation prompt */}
              {(() => {
                try {
                  // Try to parse if it's JSON arguments
                  const parsed = JSON.parse(streamingText);
                  
                  // Prioritize imagePrompt for generateSingleImage calls
                  if (parsed.imagePrompt) {
                    // Smart truncation for long prompts
                    const prompt = parsed.imagePrompt;
                    if (prompt.length > 150) {
                      return prompt.substring(0, 147) + '...';
                    }
                    return prompt;
                  }
                  
                  // Fallback to other relevant content
                  if (parsed.prompt) {
                    return parsed.prompt;
                  }
                  if (parsed.query) {
                    return parsed.query;
                  }
                  if (parsed.content) {
                    return parsed.content;
                  }
                  
                  // Only show productDescription if no better option
                  if (parsed.productDescription && !parsed.imagePrompt) {
                    return `Product: ${parsed.productDescription}`;
                  }
                  
                  return streamingText;
                } catch {
                  // If not JSON, return as-is but cleaned up
                  return streamingText;
                }
              })()}
            </div>
          </div>
        )}

        {/* Progress Percentage */}
        {typeof progress === 'number' && (
          <div className="text-xs text-gray-500 text-right">
            {Math.round(progress)}%
          </div>
        )}

        {/* Error Actions */}
        {state === 'error' && onRetry && (
          <div className="mt-3 flex justify-end">
            <button
              onClick={onRetry}
              className="text-xs px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors duration-200"
            >
              Retry
            </button>
          </div>
        )}

        {/* Success Actions */}
        {state === 'success' && (
          <div className="mt-2">
            <div className="h-0.5 bg-green-200 rounded-full">
              <div className="h-full bg-green-500 rounded-full" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

ToolCallProgress.displayName = 'ToolCallProgress';

// Add shimmer keyframes to global styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = `
    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
  `;
  document.head.appendChild(styleSheet);
}

export default ToolCallProgress;
