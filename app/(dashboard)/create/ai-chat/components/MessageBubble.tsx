"use client";

import React, { memo, useMemo } from 'react';
import { Message, ContentItem } from '../types/conversation';
import { Clock, CheckCircle, XCircle, Bot } from 'lucide-react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { useUser } from '@clerk/nextjs';
import dynamic from 'next/dynamic';

// Dynamically import MarkdownRenderer to avoid SSR issues with Shiki
const MarkdownRenderer = dynamic(() => import('./MarkdownRenderer'), { 
  ssr: false,
  loading: () => <div className="animate-pulse">Loading...</div>
});

interface MessageBubbleProps {
  message: Message;
  isLatest?: boolean;
}

// Memoized function call component
const FunctionCallDisplay = memo<{ functionCall: any }>(({ functionCall }) => {
  const { name, arguments: args } = functionCall;
  
  return (
    <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
      <div className="flex items-center gap-2 mb-2">
        <Clock size={14} className="text-gray-600" />
        <span className="text-sm font-medium text-black">
          {name === 'generateImages' ? 'Generating Images...' : 'Processing...'}
        </span>
      </div>
      
      {name === 'generateImages' && (
        <div className="text-sm text-gray-700">
          Creating {args.count} image{args.count !== 1 ? 's' : ''} with your specifications
        </div>
      )}
    </div>
  );
});

FunctionCallDisplay.displayName = 'FunctionCallDisplay';

// Memoized function result component
const FunctionResultDisplay = memo<{ functionResult: any }>(({ functionResult }) => {
  const { success, error, data } = functionResult;

  if (!success && error) {
    return (
      <div className="mt-2 p-3 bg-gray-50 border border-gray-300 rounded-lg">
        <div className="flex items-center gap-2 mb-1">
          <XCircle size={14} className="text-gray-600" />
          <span className="text-sm font-medium text-black">Error</span>
        </div>
        <div className="text-sm text-gray-700">{error}</div>
      </div>
    );
  }

  if (success && data) {
    return (
      <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="flex items-center gap-2 mb-1">
          <CheckCircle size={14} className="text-gray-600" />
          <span className="text-sm font-medium text-black">
            {(data.images || data.image) ? 'Images Generated Successfully' : 'Completed'}
          </span>
        </div>
        
        {data.creditsUsed && (
          <div className="text-sm text-gray-700">
            Used {data.creditsUsed} credit{data.creditsUsed !== 1 ? 's' : ''} •
            {data.creditsRemaining} remaining
          </div>
        )}
      </div>
    );
  }

  return null;
});

FunctionResultDisplay.displayName = 'FunctionResultDisplay';

// Memoized images component for OpenAI format
const MessageImages = memo<{ content: ContentItem[] }>(({ content }) => {
  const imageItems = content.filter(item => item.type === 'image_url');
  
  if (imageItems.length === 0) return null;
  
  return (
    <div className="mt-3 space-y-2">
      {imageItems.map((item, index) => (
        <div key={index} className="relative">
          <AspectRatio ratio={1} className="max-w-xs">
            <img
              src={item.image_url!.url}
              alt={`Uploaded image ${index + 1}`}
              className="w-full h-full object-cover rounded-lg border border-gray-200"
              loading="lazy"
            />
          </AspectRatio>
        </div>
      ))}
    </div>
  );
});

MessageImages.displayName = 'MessageImages';

// Helper function to extract text content
const getTextContent = (content: string | ContentItem[]): string => {
  if (typeof content === 'string') {
    return content;
  }
  
  return content
    .filter(item => item.type === 'text')
    .map(item => item.text)
    .join('\n');
};

// Memoized generated images component for function results
const GeneratedImages = memo<{ functionResult: any }>(({ functionResult }) => {
  if (!functionResult?.success || !functionResult?.data) {
    return null;
  }

  const { data } = functionResult;
  let images = [];

  // Handle both old format (data.images) and new format (data.image)
  if (data.images) {
    images = data.images;
  } else if (data.image) {
    images = [data.image];
  }

  if (images.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 space-y-2">
      {images.map((image: any, index: number) => (
        <div key={image.id || index} className="relative">
          <AspectRatio ratio={1} className="max-w-xs">
            <img
              src={image.url}
              alt={`Generated image ${index + 1}`}
              className="w-full h-full object-cover rounded-lg border border-gray-200"
              loading="lazy"
            />
          </AspectRatio>
        </div>
      ))}
    </div>
  );
});

GeneratedImages.displayName = 'GeneratedImages';

// Memoized user avatar component
const UserAvatar = memo<{ user: any }>(({ user }) => {
  return (
    <div className="flex-shrink-0 w-6 h-6 rounded-full overflow-hidden mt-1">
      {user?.imageUrl ? (
        <img
          src={user.imageUrl}
          alt="User avatar"
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="w-full h-full bg-gray-300 rounded-full flex items-center justify-center">
          <span className="text-xs font-medium text-gray-600">
            {user?.firstName?.[0] || user?.fullName?.[0] || 'U'}
          </span>
        </div>
      )}
    </div>
  );
});

UserAvatar.displayName = 'UserAvatar';

// Main MessageBubble component with performance optimizations
const MessageBubble = memo<MessageBubbleProps>(({ message, isLatest }) => {
  const { user } = useUser();

  // Memoize message classification to prevent recalculation
  const messageClassification = useMemo(() => {
    const isUser = message.type === 'user';
    const isAgent = message.type === 'agent';
    
    // Check for images in OpenAI format
    const hasImages = isUser && Array.isArray(message.content) && 
      message.content.some(item => item.type === 'image_url');
    
    return {
      isUser,
      isAgent,
      hasImages,
      hasFunctionCall: message.functionCall && !message.functionResult,
      hasFunctionResult: !!message.functionResult,
      hasGeneratedImages: isAgent && message.functionResult?.data && (message.functionResult.data.images || message.functionResult.data.image),
      isLoading: isLatest && message.isLoading
    };
  }, [message.type, message.content, message.functionCall, message.functionResult, isLatest, message.isLoading]);

  // Memoized CSS classes to prevent recalculation
  const containerClasses = useMemo(() => 
    `flex gap-3 mb-6 ${messageClassification.isUser ? 'justify-end' : 'justify-start'}`,
    [messageClassification.isUser]
  );

  const bubbleClasses = useMemo(() => `
    ${messageClassification.isUser
      ? 'bg-gray-100 text-black ml-auto rounded-3xl px-4 py-3'
      : 'text-black'
    }
    ${messageClassification.isLoading ? 'animate-pulse' : ''}
  `, [messageClassification.isUser, messageClassification.isLoading]);

  const contentClasses = useMemo(() => 
    `max-w-[85%] ${messageClassification.isUser ? 'order-first' : ''}`,
    [messageClassification.isUser]
  );

  return (
    <div className={containerClasses}>
      {/* Avatar - for agent */}
      {messageClassification.isAgent && (
        <div className="flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center mt-1">
          <Bot size={14} className="text-gray-600" />
        </div>
      )}

      {/* Message Content */}
      <div className={contentClasses}>
        {/* Message Bubble */}
        <div className={bubbleClasses}>
          {/* Text Content */}
          {messageClassification.isAgent ? (
            <MarkdownRenderer 
              content={getTextContent(message.content)} 
              isStreamFinished={!message.isLoading}
            />
          ) : (
            <div className="whitespace-pre-wrap text-[15px] leading-relaxed">
              {getTextContent(message.content)}
            </div>
          )}

          {/* Function Call Status */}
          {messageClassification.hasFunctionCall && (
            <FunctionCallDisplay functionCall={message.functionCall} />
          )}

          {/* Function Result */}
          {messageClassification.hasFunctionResult && (
            <FunctionResultDisplay functionResult={message.functionResult} />
          )}
        </div>

        {/* User Uploaded Images */}
        {messageClassification.hasImages && Array.isArray(message.content) && (
          <MessageImages content={message.content} />
        )}

        {/* Generated Images from Function Results */}
        {messageClassification.hasGeneratedImages && (
          <GeneratedImages functionResult={message.functionResult} />
        )}
      </div>

      {/* Avatar - for user (positioned after content) */}
      {messageClassification.isUser && (
        <UserAvatar user={user} />
      )}
    </div>
  );
});

MessageBubble.displayName = 'MessageBubble';

export default MessageBubble;
