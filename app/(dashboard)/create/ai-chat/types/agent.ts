export interface AgentResponse {
  message: string;
  functionCalls?: AgentFunctionCall[];
  suggestedActions?: SuggestedAction[];
  images?: string[];
}

export interface AgentFunctionCall {
  name: string;
  arguments: Record<string, any>;
  id: string;
}

export interface SuggestedAction {
  id: string;
  label: string;
  action: () => void;
  icon?: string;
}

export interface ImageGenerationParams {
  productImageUrl: string;
  count: number;
  showPerson?: boolean;
  modelType?: 'female' | 'male' | 'any';
  ethnicity?: 'any' | 'caucasian' | 'african' | 'asian' | 'hispanic';
  vibe?: 'casual' | 'elegant' | 'edgy' | 'minimalist' | 'vintage';
  photoStyle?: 'editorial' | 'commercial' | 'lifestyle' | 'portrait';
  background?: 'studio' | 'outdoor' | 'urban' | 'nature' | 'minimal';
  aspectRatio?: 'square' | 'portrait' | 'landscape';
  customInstructions?: string;
}

export interface UserMemory {
  id: string;
  content: string;
  category: 'brand_style' | 'preferences' | 'feedback' | 'goals' | 'audience' | 'general';
  context?: string;
  createdAt: string;
}

export interface AgentTool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, any>;
      required: string[];
      additionalProperties: boolean;
    };
    strict: boolean;
  };
}

export interface ConversationContext {
  hasProductImage: boolean;
  requirements: Partial<ImageGenerationParams>;
  userCredits: number;
  conversationStage: 'welcome' | 'gathering_requirements' | 'ready_to_generate' | 'generating' | 'reviewing_results';
  memories?: UserMemory[];
}