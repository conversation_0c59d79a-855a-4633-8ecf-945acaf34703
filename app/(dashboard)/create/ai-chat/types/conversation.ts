export interface ContentItem {
  type: 'text' | 'image_url';
  text?: string;
  image_url?: {
    url: string;
  };
}

export interface Message {
  id: string;
  type: 'user' | 'agent';
  content: string | ContentItem[]; // Support OpenAI format
  timestamp: Date;
  functionCall?: FunctionCall;
  functionResult?: FunctionResult;
  isLoading?: boolean;
}

export interface FunctionCall {
  name: string;
  arguments: Record<string, any>;
}

export interface FunctionResult {
  success: boolean;
  data?: any;
  error?: string;
}

export interface ImageRequirements {
  showPerson?: boolean;
  modelType?: 'female' | 'male' | 'any';
  ethnicity?: 'any' | 'caucasian' | 'african' | 'asian' | 'hispanic';
  vibe?: 'casual' | 'elegant' | 'edgy' | 'minimalist' | 'vintage';
  photoStyle?: 'editorial' | 'commercial' | 'lifestyle' | 'portrait';
  background?: 'studio' | 'outdoor' | 'urban' | 'nature' | 'minimal';
  aspectRatio?: 'square' | 'portrait' | 'landscape';
  customInstructions?: string;
}

export interface GeneratedImage {
  id: string;
  url: string;
  createdAt: string;
  prompt?: string;
}

export interface ConversationSession {
  productImage?: string;
  requirements?: ImageRequirements;
  generationStatus: 'idle' | 'generating' | 'complete' | 'error';
  generatedImages: GeneratedImage[];
  uploadedFiles: File[];
}

export interface ConversationState {
  messages: Message[];
  currentSession: ConversationSession;
  userContext?: UserContext;
  isTyping: boolean;
  isConnected: boolean;
}

export interface UserContext {
  id: string;
  email: string;
  name?: string;
  credits: number;
  subscription?: {
    status: string;
    plan: string;
  };
  recentImages?: GeneratedImage[];
}
