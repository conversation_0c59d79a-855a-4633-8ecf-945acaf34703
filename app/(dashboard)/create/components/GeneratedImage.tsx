// app/(dashboard)/create/components/GeneratedImage.tsx
"use client";

import { useState, useEffect } from 'react';
import { Download, X, Link as LinkIcon } from 'lucide-react';
import Button from '../../components/Button';
import { Skeleton } from '@/components/ui/skeleton';

import { toast } from 'sonner';

export default function GeneratedImage({ 
  src, 
  alt = "Generated image",
  onDownload,
  downloadUrl,
  showActions = true
}: {
  src: string;
  alt?: string;
  onDownload?: () => void;
  downloadUrl?: string;
  showActions?: boolean;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const handleDownload = async () => {
    try {
      if (onDownload) {
        onDownload();
        return;
      }
      
      // The URL to download from (either the provided downloadUrl or the src)
      const url = downloadUrl || src;
      
      // Create a filename for the download
      const filename = `market-me-image-${Date.now()}.webp`;
      
      // Fetch the image as a blob to handle CORS issues
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }
      
      const blob = await response.blob();
      
      // Create object URL from blob
      const objectUrl = URL.createObjectURL(blob);
      
      // Create and trigger download
      const link = document.createElement('a');
      link.href = objectUrl;
      link.download = filename;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up object URL
      URL.revokeObjectURL(objectUrl);
      
      console.log('[GeneratedImage] Download initiated for:', url);
      toast.success('Image downloaded successfully');
    } catch (error) {
      console.error('[GeneratedImage] Error downloading image:', error);
      toast.error('Failed to download image. Please try again.');
    }
  };
  
  const handleCopyLink = () => {
    navigator.clipboard.writeText(src)
      .then(() => toast.success('Image URL copied to clipboard'))
      .catch(err => {
        console.error('Failed to copy URL:', err);
        toast.error('Failed to copy URL to clipboard');
      });
  };
  
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  useEffect(() => {
    if (isExpanded) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isExpanded]);



  return (
    <>
      {/* Thumbnail View */}
      <div className="relative overflow-hidden group bg-gray-100 rounded-lg">
        <div className="relative w-full">
          {isLoading && (
            <Skeleton className="absolute inset-0 z-10 h-64" />
          )}
          <img
            src={src}
            alt={alt}
            className="w-full h-auto object-contain max-h-96 cursor-zoom-in"
            onClick={toggleExpand}
            onLoad={() => setIsLoading(false)}
            onError={() => setIsLoading(false)}
          />
        </div>
        
        {showActions && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex justify-end gap-2">
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  handleCopyLink();
                }}
                className="p-2 rounded-full bg-black/80 text-white hover:bg-black"
                aria-label="Copy image URL"
              >
                <LinkIcon className="w-4 h-4" />
              </button>
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownload();
                }}
                className="p-2 rounded-full bg-black/80 text-white hover:bg-black"
                aria-label="Download image"
              >
                <Download className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Expanded View */}
      {isExpanded && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4" onClick={toggleExpand}>
          <div className="relative max-w-7xl w-full max-h-[95vh] flex flex-col gap-4">
            <button 
              onClick={toggleExpand}
              className="absolute -top-12 right-0 p-2 text-white hover:text-gray-300"
              aria-label="Close expanded view"
            >
              <X className="w-6 h-6" />
            </button>
            
            <div className="flex-1 flex items-center justify-center overflow-auto min-h-[50vh]">
              <img
                src={src}
                alt={alt}
                className="max-w-full max-h-[90vh] object-contain"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
            
            <div className="flex justify-center items-center gap-4">
              <Button 
                variant="primary" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownload();
                }}
                className="flex items-center gap-2 px-6"
              >
                <Download className="w-4 h-4" />
                Download
              </Button>
              <Button 
                variant="primary" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleCopyLink();
                }}
                className="flex items-center gap-2 px-6"
              >
                <LinkIcon className="w-4 h-4" />
                Copy Link
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
