"use client";

import { useState, useEffect, useRef } from "react";
import { useUser } from "@clerk/nextjs";
import AIChatSidebar from "./ai-chat/components/AIChatSidebar";
import GeneratedImage from "./components/GeneratedImage";
import { toast } from "sonner";
import { useResponsive } from "@/hooks/useResponsive";

interface GeneratedImageData {
  id: string;
  url: string;
  createdAt: string;
  prompt?: string;
}

export default function CreatePage() {
  const { isSignedIn, isLoaded } = useUser();
  const { isMobile } = useResponsive();
  const [generatedImages, setGeneratedImages] = useState<GeneratedImageData[]>([]);
  const [hasCalledAPI, setHasCalledAPI] = useState(false);
  const generatedImagesRef = useRef<HTMLDivElement>(null);

  // Call /api/users once, if user is signed in, to ensure DB record
  useEffect(() => {
    if (!hasCalledAPI && isLoaded && isSignedIn) {
      console.log("[CreatePage] User is signed in. Calling /api/users...");

      fetch("/api/users", { method: "POST" })
        .then((res) => res.json())
        .then((data) => {
          console.log("[CreatePage] /api/users response:", data);
          setHasCalledAPI(true);
        })
        .catch((err) => {
          console.error("[CreatePage] Failed to call /api/users:", err);
        });
    }
  }, [hasCalledAPI, isLoaded, isSignedIn]);

  // Handle new images generated by AI agent
  const handleImagesGenerated = (newImages: GeneratedImageData[]) => {
    console.log("[CreatePage] New images received:", newImages);

    // Add new images to the beginning of the array
    setGeneratedImages((prev) => [...newImages, ...prev]);

    // Show success toast
    toast.success(
      `Generated ${newImages.length} image${newImages.length !== 1 ? "s" : ""} successfully!`
    );

    // Scroll to results
    setTimeout(() => {
      generatedImagesRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  // Handle structured output from AI agent
  const handleStructuredOutput = (output: any) => {
    console.log("[CreatePage] Received structured output:", output);

    if (output.type === "image_generation" && output.data.success) {
      // Handle both old format (data.images) and new format (data.image)
      let newImages = [];
      
      if (output.data.images) {
        // Old format - array of images
        newImages = output.data.images;
      } else if (output.data.image) {
        // New format - single image object
        newImages = [output.data.image];
      }
      
      if (newImages.length > 0) {
        handleImagesGenerated(newImages);
      }
    }
  };

  // Render empty state for results panel
  const renderEmptyState = () => (
    <div className="h-full flex flex-col items-center justify-center p-8">
      <div className="text-center max-w-md">
        {/* Heading */}
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Your AI Marketing Agent is Ready
        </h3>

        {/* Description */}
        <p className="text-gray-600 mb-6 text-sm leading-relaxed">
          Meet Yaya, your intelligent marketing assistant with memory capabilities.
          Simply chat about your product and marketing goals - Yaya will remember your preferences
          and guide you through creating stunning content.
        </p>

        {/* Features */}
        <div className="space-y-3 text-center">
          <div className="text-sm text-gray-600">
            💭 Remembers your brand preferences and feedback
          </div>

          <div className="text-sm text-gray-600">
            🎨 Generates personalized marketing content
          </div>

          <div className="text-sm text-gray-600">
            📈 Provides strategic marketing advice
          </div>
        </div>

        {/* CTA */}
        <div className="mt-8 p-3 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-sm text-gray-700">
            Yaya is ready to build a lasting creative partnership with you
          </p>
        </div>
      </div>
    </div>
  );

  // Render loading state
  if (!isLoaded) {
    return (
      <div className="h-full bg-white rounded-lg shadow-sm border border-gray-200 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-500">Loading your AI assistant...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full bg-white ${isMobile ? 'overflow-hidden' : 'rounded-lg shadow-sm border border-gray-200'}`}>
      {/* Content area - Responsive layout */}
      <div className="flex h-full overflow-hidden">
        {/* Left Panel - Image Display (Desktop Only) */}
        <div className="hidden lg:flex lg:w-2/3 flex-col">
          <div className="flex-1 overflow-y-auto">
            {generatedImages.length > 0 ? (
              <div ref={generatedImagesRef} className="p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Generated Images
                  </h3>
                  <p className="text-sm text-gray-600">
                    {generatedImages.length} image{generatedImages.length !== 1 ? "s" : ""} created
                  </p>
                </div>
  
                {/* Images Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {generatedImages.map((image, index) => (
                    <div key={`${image.id}-${index}`} className="relative group">
                      <GeneratedImage
                        src={image.url}
                        alt={`Generated image ${index + 1}`}
                      />
  
                      {/* Image Info Overlay */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <div className="text-white text-xs">
                          <div className="font-medium mb-1">
                            {new Date(image.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
  
                {/* Pagination or Load More could go here */}
                {generatedImages.length >= 10 && (
                  <div className="mt-8 text-center">
                    <p className="text-sm text-gray-500">
                      Visit the Library to see all your generated images
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-6">{renderEmptyState()}</div>
            )}
          </div>
        </div>

        {/* AI Chat Interface - Full width on mobile, 1/3 width on desktop */}
        <div className="w-full lg:w-1/3 flex flex-col lg:border-l border-gray-200 min-h-0">
          <AIChatSidebar
            onStructuredOutput={handleStructuredOutput}
            isMobile={isMobile}
          />
        </div>
      </div>
    </div>
  );
}
