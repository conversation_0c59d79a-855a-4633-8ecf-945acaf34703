"use client";

import TabHeader from '../components/TabHeader';
import CreditBalance from './components/CreditBalance';
import PricingPlans from './components/PricingPlans';
import CheckoutSuccess from './components/CheckoutSuccess';
import { useResponsive } from '@/hooks/useResponsive';

export default function SettingsPage() {
  const { isMobile } = useResponsive();

  return (
    <div className={`flex flex-col items-center ${isMobile ? 'px-3' : 'px-4'} pt-2`}>
      <TabHeader
        title="Settings"
        subtitle="Manage your account and preferences"
        alignment="center"
      />
      
      <div className={`w-full ${isMobile ? 'max-w-full' : 'max-w-4xl'} mt-4 ${isMobile ? 'space-y-4' : 'space-y-6'}`}>
        {/* Checkout success/error message */}
        <CheckoutSuccess />
        
        {/* Credit balance */}
        <CreditBalance />
        
        {/* Pricing plans */}
        <PricingPlans />
      </div>
    </div>
  );
}