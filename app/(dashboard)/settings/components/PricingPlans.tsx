// app/(dashboard)/settings/components/PricingPlans.tsx
"use client";

import { useState, useEffect } from 'react';
import { Check, CreditCard, Settings } from 'lucide-react';
import Button from '../../components/Button';
import { useResponsive } from '@/hooks/useResponsive';

interface SubscriptionPlan {
  id: string;
  name: string;
  monthlyPrice: number;
  monthlyCredits: number;
  costPerCredit: number;
  mostPopular?: boolean;
}

interface UserSubscription {
  status: string;
  plan_id: string;
  monthly_credits: number;
  current_period_end: string | Date;
}

export default function PricingPlans() {
  const [loading, setLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [hasSubscription, setHasSubscription] = useState(false);

  // Define subscription plans
  const plans: SubscriptionPlan[] = [
    {
      id: "basic",
      name: "Basic",
      monthlyPrice: 49,
      monthlyCredits: 50,
      costPerCredit: 0.98
    },
    {
      id: "growth",
      name: "Growth",
      monthlyPrice: 199,
      monthlyCredits: 230,
      costPerCredit: 0.87,
      mostPopular: true
    },
    {
      id: "pro",
      name: "Pro",
      monthlyPrice: 399,
      monthlyCredits: 480,
      costPerCredit: 0.83
    }
  ];

  // Fetch current subscription status
  useEffect(() => {
    fetchSubscriptionStatus();
    
    // Check if returning from successful checkout
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('subscription_success') === 'true') {
      // Poll for status updates for incomplete subscriptions
      const pollInterval = setInterval(async () => {
        await fetchSubscriptionStatus();
        // Stop polling after 30 seconds or when subscription becomes active
        if (subscription?.status === 'active') {
          clearInterval(pollInterval);
        }
      }, 2000);
      
      // Clear interval after 30 seconds max
      setTimeout(() => clearInterval(pollInterval), 30000);
      
      return () => clearInterval(pollInterval);
    }
  }, [subscription?.status]);

  // Auto-refresh if subscription is incomplete
  useEffect(() => {
    if (subscription?.status === 'incomplete') {
      const pollInterval = setInterval(fetchSubscriptionStatus, 3000);
      setTimeout(() => clearInterval(pollInterval), 15000); // Stop after 15 seconds
      return () => clearInterval(pollInterval);
    }
  }, [subscription?.status]);

  async function fetchSubscriptionStatus() {
    try {
      const response = await fetch('/api/subscriptions/checkout', {
        method: 'GET',
      });
      
      if (response.ok) {
        const data = await response.json();
        setHasSubscription(data.hasSubscription);
        setSubscription(data.subscription);
      }
    } catch (err) {
      console.error('Failed to fetch subscription status:', err);
    }
  }

  // Handle subscription checkout
  async function handleSubscription(planId: string) {
    setSelectedPlan(planId);
    setLoading(true);
    setError(null);

    try {
      console.log(`[PricingPlans] Starting subscription checkout for plan: ${planId}`);
      const response = await fetch('/api/subscriptions/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to create subscription checkout');
      }

      const { url } = await response.json();
      
      // Redirect to Stripe checkout
      console.log(`[PricingPlans] Redirecting to Stripe subscription checkout: ${url}`);
      window.location.href = url;
    } catch (err) {
      console.error('[PricingPlans] Subscription checkout error:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setLoading(false);
    }
  }

  // Handle subscription management (customer portal)
  async function handleManageSubscription() {
    try {
      // This will redirect to Stripe's customer portal
      // You'll need to implement this endpoint
      const response = await fetch('/api/subscriptions/portal', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to access customer portal');
      }

      const { url } = await response.json();
      window.location.href = url;
    } catch (err) {
      console.error('Failed to open customer portal:', err);
      setError('Failed to open subscription management');
    }
  }

  // Calculate savings compared to traditional photoshoots
  const calculateSavings = (credits: number) => {
    // Average cost per image in traditional photoshoots ($87 for mass market)
    const traditionalCostPerImage = 87;
    const traditionalTotal = credits * traditionalCostPerImage;
    const ourTotal = credits * 1; // Our price is roughly $1 per image
    
    return Math.round((traditionalTotal - ourTotal) / traditionalTotal * 100);
  };

  const { isMobile } = useResponsive();

  return (
    <div className={`border rounded-lg ${isMobile ? 'p-4' : 'p-6'} bg-white`}>
      <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'} ${isMobile ? 'mb-3' : 'mb-4'}`}>
        <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-medium`}>Subscription Plans</h3>
        {hasSubscription && subscription && (
          <Button
            onClick={handleManageSubscription}
            variant="secondary"
            className={`${isMobile ? 'w-full py-3 text-base' : 'text-sm'} ${isMobile ? 'min-h-[44px]' : ''}`}
          >
            <Settings size={isMobile ? 20 : 16} className={`${isMobile ? 'mr-2' : 'mr-1'}`} />
            Manage Subscription
          </Button>
        )}
      </div>

      {hasSubscription && subscription && (
        <div className={`${isMobile ? 'mb-3' : 'mb-4'} p-4 border rounded-md ${
          subscription.status === 'canceled'
            ? 'bg-red-50 border-red-200'
            : 'bg-blue-50 border-blue-200'
        }`}>
          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'items-center justify-between'}`}>
            <div>
              <p className={`font-medium ${isMobile ? 'text-base' : ''} ${
                subscription.status === 'canceled' ? 'text-red-900' : 'text-blue-900'
              }`}>
                {subscription.status === 'canceled'
                  ? `Canceled Plan: ${subscription.plan_id.charAt(0).toUpperCase() + subscription.plan_id.slice(1)}`
                  : `Current Plan: ${subscription.plan_id.charAt(0).toUpperCase() + subscription.plan_id.slice(1)}`
                }
              </p>
              <p className={`${isMobile ? 'text-sm' : 'text-sm'} ${
                subscription.status === 'canceled' ? 'text-red-700' : 'text-blue-700'
              }`}>
                {subscription.status === 'canceled'
                  ? 'Subscription canceled - you keep all existing credits'
                  : subscription.status === 'incomplete'
                    ? '🔄 Processing subscription... Credits will be added shortly'
                    : `${subscription.monthly_credits} credits added monthly • Active subscription`
                }
              </p>
            </div>
            <div className={`${isMobile ? 'text-left' : 'text-right'}`}>
              {subscription.status === 'canceled' ? (
                <p className={`${isMobile ? 'text-sm' : 'text-sm'} text-red-600`}>
                  Canceled
                </p>
              ) : (
                <p className={`${isMobile ? 'text-sm' : 'text-sm'} text-blue-600`}>
                  Next billing: {new Date(subscription.current_period_end).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  })}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
      
      {error && (
        <div className={`${isMobile ? 'mb-3' : 'mb-4'} p-3 bg-red-50 border border-red-200 text-red-700 rounded-md ${isMobile ? 'text-sm' : ''}`}>
          {error}
        </div>
      )}
      
      <div className={`grid ${isMobile ? 'grid-cols-1 gap-3' : 'grid-cols-1 md:grid-cols-3 gap-4'}`}>
        {plans.map((plan) => {
          const isCurrentPlan = subscription?.plan_id === plan.id && subscription?.status !== 'canceled';
          const currentPlan = subscription ? plans.find(p => p.id === subscription.plan_id) : undefined;
          const isUpgrade = subscription && subscription.status !== 'canceled' && currentPlan && currentPlan.monthlyPrice < plan.monthlyPrice;
          const isCanceled = subscription?.status === 'canceled';
          
          return (
            <div
              key={plan.id}
              className={`border rounded-lg ${isMobile ? 'p-4' : 'p-4'} flex flex-col relative transition-all
                ${plan.mostPopular ? 'border-blue-500 shadow-md' : 'border-gray-200 hover:border-gray-300'}
                ${isCurrentPlan ? 'border-green-500 bg-green-50' : ''}
              `}
            >
              {plan.mostPopular && !isCurrentPlan && (
                <div className={`absolute top-0 right-0 -mt-2 -mr-2 bg-blue-500 text-white px-2 py-1 rounded-md text-xs font-medium`}>
                  Most Popular
                </div>
              )}
              
              {isCurrentPlan && (
                <div className={`absolute top-0 right-0 -mt-2 -mr-2 bg-green-500 text-white px-2 py-1 rounded-md text-xs font-medium`}>
                  Current Plan
                </div>
              )}
              
              <h4 className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold`}>{plan.name}</h4>
              <div className={`mt-2 ${isMobile ? 'text-2xl' : 'text-3xl'} font-bold`}>${plan.monthlyPrice}</div>
              <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 mt-1`}>per month</div>
              
              <div className={`mt-4 ${isMobile ? 'text-base' : 'text-lg'} font-medium text-gray-800`}>
                {plan.monthlyCredits.toLocaleString()} credits/month
              </div>
              <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}>
                ${plan.costPerCredit.toFixed(2)} per credit
              </div>
              
              <div className={`mt-4 flex-grow`}>
                <ul className={`${isMobile ? 'space-y-3' : 'space-y-2'}`}>
                  <li className="flex items-start">
                    <Check size={isMobile ? 18 : 16} className={`text-green-500 ${isMobile ? 'mt-0' : 'mt-0.5'} mr-2 shrink-0`} />
                    <span className={`${isMobile ? 'text-sm' : 'text-sm'}`}>{plan.monthlyCredits} AI-generated marketing images monthly</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={isMobile ? 18 : 16} className={`text-green-500 ${isMobile ? 'mt-0' : 'mt-0.5'} mr-2 shrink-0`} />
                    <span className={`${isMobile ? 'text-sm' : 'text-sm'}`}>Credits accumulate and never expire</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={isMobile ? 18 : 16} className={`text-green-500 ${isMobile ? 'mt-0' : 'mt-0.5'} mr-2 shrink-0`} />
                    <span className={`${isMobile ? 'text-sm' : 'text-sm'}`}>{calculateSavings(plan.monthlyCredits)}% savings vs. traditional photoshoots</span>
                  </li>
                </ul>
              </div>
              
              <Button
                className={`${isMobile ? 'mt-6 py-3 text-base' : 'mt-4'} w-full ${isMobile ? 'min-h-[44px]' : ''}`}
                onClick={() => handleSubscription(plan.id)}
                isLoading={loading && selectedPlan === plan.id}
                disabled={loading || isCurrentPlan}
                variant={plan.mostPopular ? "primary" : "secondary"}
              >
                {isCurrentPlan ? (
                  'Current Plan'
                ) : loading && selectedPlan === plan.id ? (
                  'Processing...'
                ) : isCanceled ? (
                  <>
                    <CreditCard size={isMobile ? 20 : 16} className={`${isMobile ? 'mr-2' : 'mr-1'}`} />
                    Reactivate
                  </>
                ) : isUpgrade ? (
                  <>
                    <CreditCard size={isMobile ? 20 : 16} className={`${isMobile ? 'mr-2' : 'mr-1'}`} />
                    Upgrade
                  </>
                ) : hasSubscription ? (
                  <>
                    <CreditCard size={isMobile ? 20 : 16} className={`${isMobile ? 'mr-2' : 'mr-1'}`} />
                    Change Plan
                  </>
                ) : (
                  <>
                    <CreditCard size={isMobile ? 20 : 16} className={`${isMobile ? 'mr-2' : 'mr-1'}`} />
                    Subscribe
                  </>
                )}
              </Button>
            </div>
          );
        })}
      </div>
      
      <div className={`${isMobile ? 'mt-4' : 'mt-6'} ${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 ${isMobile ? 'space-y-2' : ''}`}>
        <p>Secure payment processing by Stripe. All prices in USD.</p>
        <p>Credits accumulate each month and never expire. Cancel anytime.</p>
        <p className={`${isMobile ? 'mt-2' : 'mt-2'}`}>Each credit generates one professional marketing image. With our batch generation feature, you can create 8 unique variations in one go.</p>
      </div>
    </div>
  );
}
