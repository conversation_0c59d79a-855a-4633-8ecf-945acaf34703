// File: app/(dashboard)/settings/components/CheckoutSuccess.tsx

import { useEffect, useState, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useResponsive } from '@/hooks/useResponsive';

export default function CheckoutSuccess() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [message, setMessage] = useState<string | null>(null);
  const [status, setStatus] = useState<'success' | 'error' | null>(null);
  const [loading, setLoading] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Check for success or canceled params
    const success = searchParams.get('checkout_success');
    const canceled = searchParams.get('checkout_canceled');
    const sessionId = searchParams.get('session_id');
    const plan = searchParams.get('plan');
    const credits = searchParams.get('credits');
    
    console.log("[CheckoutSuccess] URL params:", { 
      success, 
      canceled, 
      sessionId, 
      plan,
      credits,
      allParams: Object.fromEntries(searchParams.entries())
    });
    
    // Don't do anything if no relevant params
    if (!success && !canceled && !sessionId) {
      return;
    }
    
    if (canceled === 'true') {
      setStatus('error');
      setMessage('Checkout was canceled. No payment was processed.');
      
      // Clear URL params after a delay
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        router.push('/settings');
      }, 5000);
      return;
    }
    
    // If success=true and we have a sessionId, verify the payment
    if (success === 'true' && sessionId) {
      console.log(`[CheckoutSuccess] Verifying payment for session: ${sessionId}`);
      
      const verifyPayment = async () => {
        setLoading(true);
        try {
          const response = await fetch(`/api/checkout?session_id=${sessionId}`);
          console.log(`[CheckoutSuccess] Verification response status:`, response.status);
          
          if (response.ok) {
            const data = await response.json();
            console.log(`[CheckoutSuccess] Verification response data:`, data);
            
            if (data.success) {
              setStatus('success');
              if (data.newBalance) {
                setMessage(`Payment successful! Added ${data.credits} credits to your account. New balance: ${data.newBalance} credits.`);
              } else {
                setMessage(`Payment successful! Added ${data.credits} credits to your account.`);
              }
              
              // Force a refresh of the credits display elsewhere in the app
              window.dispatchEvent(new Event('creditUpdated'));
            } else {
              setStatus('error');
              setMessage(data.error || 'Payment verification failed. Please contact support.');
            }
          } else {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            console.error(`[CheckoutSuccess] Error verifying payment:`, errorData);
            
            setStatus('error');
            setMessage(errorData.error || 'Error verifying payment. Please contact support.');
          }
        } catch (err) {
          console.error('[CheckoutSuccess] Exception during payment verification:', err);
          setStatus('error');
          setMessage('Error verifying payment. Please contact support.');
        } finally {
          setLoading(false);
          
          // Clear URL params after a delay
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
          }
          timeoutRef.current = setTimeout(() => {
            router.push('/settings');
          }, 5000);
        }
      };
      
      verifyPayment();
    } 
    // If success=true but no sessionId, show a message but don't attempt verification
    else if (success === 'true' && plan && credits) {
      setStatus('success');
      setMessage(`Successfully purchased ${credits} credits with the ${plan.charAt(0).toUpperCase() + plan.slice(1)} plan! We're processing your payment now.`);
      
      // Clear URL params after a delay
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        router.push('/settings');
      }, 5000);
    }
  }, [searchParams, router]);

  // Clean up timeout when component unmounts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const { isMobile } = useResponsive();

  // Don't render anything if no message
  if (!status && !message && !loading) return null;

  return (
    <div className={`${isMobile ? 'mb-4' : 'mb-6'} ${isMobile ? 'p-3' : 'p-4'} border rounded-md flex items-start ${isMobile ? 'space-x-2' : 'space-x-3'}
      ${loading ? 'bg-blue-50 border-blue-200' :
        status === 'success' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}
    >
      {loading ? (
        <Loader2 className={`text-blue-500 shrink-0 ${isMobile ? 'mt-0' : 'mt-0.5'} animate-spin`} size={isMobile ? 18 : 20} />
      ) : status === 'success' ? (
        <CheckCircle className={`text-green-500 shrink-0 ${isMobile ? 'mt-0' : 'mt-0.5'}`} size={isMobile ? 18 : 20} />
      ) : (
        <XCircle className={`text-red-500 shrink-0 ${isMobile ? 'mt-0' : 'mt-0.5'}`} size={isMobile ? 18 : 20} />
      )}
      
      <div className="flex-1">
        <h3 className={`font-medium ${isMobile ? 'text-sm' : ''}
          ${loading ? 'text-blue-700' :
            status === 'success' ? 'text-green-700' : 'text-red-700'}`}
        >
          {loading ? 'Processing Payment' :
            status === 'success' ? 'Payment Successful' : 'Payment Failed'}
        </h3>
        <p className={`${isMobile ? 'text-xs leading-relaxed' : 'text-sm'}
          ${loading ? 'text-blue-600' :
            status === 'success' ? 'text-green-600' : 'text-red-600'}`}
        >
          {loading ? 'Verifying your payment...' : message}
        </p>
      </div>
    </div>
  );
}