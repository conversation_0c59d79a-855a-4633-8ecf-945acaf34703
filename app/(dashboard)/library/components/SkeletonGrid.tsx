"use client";

import { Skeleton } from '@/components/ui/skeleton';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { useResponsive } from '@/hooks/useResponsive';

export default function SkeletonGrid() {
  const { isMobile, isTablet } = useResponsive();
  
  // Create an array of skeleton items - fewer on mobile for faster loading perception
  const skeletonCount = isMobile ? 4 : 8;
  const skeletonItems = Array.from({ length: skeletonCount }, (_, i) => i);

  // Dynamic grid columns based on screen size
  const getGridCols = () => {
    if (isMobile) return 'grid-cols-1 sm:grid-cols-2';
    if (isTablet) return 'grid-cols-2 md:grid-cols-3';
    return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
  };
  
  // Dynamic gap based on screen size
  const getGridGap = () => {
    if (isMobile) return 'gap-4';
    return 'gap-6';
  };

  return (
    <div className={`grid ${getGridCols()} ${getGridGap()}`}>
      {skeletonItems.map((index) => (
        <div
          key={index}
          className="bg-white border border-gray-200 rounded-[25px] overflow-hidden"
        >
          <AspectRatio ratio={1}>
            <Skeleton className="w-full h-full" />
          </AspectRatio>
          <div className={`${isMobile ? 'p-3' : 'p-4'} space-y-3`}>
            <Skeleton className={`${isMobile ? 'h-3' : 'h-4'} w-3/4`} />
            <Skeleton className={`${isMobile ? 'h-2' : 'h-3'} w-1/2`} />
          </div>
        </div>
      ))}
    </div>
  );
}
