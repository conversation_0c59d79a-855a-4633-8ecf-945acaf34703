"use client";

import { memo } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { useResponsive } from '@/hooks/useResponsive';

interface OptimizedSkeletonGridProps {
  count?: number;
}

const OptimizedSkeletonGrid = memo(function OptimizedSkeletonGrid({ 
  count 
}: OptimizedSkeletonGridProps) {
  const { isMobile, isTablet } = useResponsive();
  
  // Adaptive skeleton count based on device for optimal loading perception
  const skeletonCount = count || (isMobile ? 4 : isTablet ? 6 : 8);
  const skeletonItems = Array.from({ length: skeletonCount }, (_, i) => i);

  // Consistent grid layout with main components
  const getGridCols = () => {
    if (isMobile) return 'grid-cols-1 sm:grid-cols-2';
    if (isTablet) return 'grid-cols-2 md:grid-cols-3';
    return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
  };
  
  const getGridGap = () => {
    if (isMobile) return 'gap-4';
    return 'gap-6';
  };

  return (
    <div className={`grid ${getGridCols()} ${getGridGap()} animate-pulse`}>
      {skeletonItems.map((index) => (
        <div
          key={index}
          className="bg-white border border-gray-200 rounded-[25px] overflow-hidden"
        >
          {/* Image skeleton with proper aspect ratio */}
          <AspectRatio ratio={1}>
            <Skeleton className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200" />
          </AspectRatio>
          
          {/* Content skeleton */}
          <div className={`${isMobile ? 'p-3' : 'p-4'} space-y-3`}>
            <div className="flex justify-between items-start">
              <Skeleton className={`${isMobile ? 'h-3' : 'h-4'} w-20 bg-gray-300`} />
              <div className="flex gap-1">
                <Skeleton className={`${isMobile ? 'w-8 h-8' : 'w-6 h-6'} rounded bg-gray-300`} />
                <Skeleton className={`${isMobile ? 'w-8 h-8' : 'w-6 h-6'} rounded bg-gray-300`} />
                <Skeleton className={`${isMobile ? 'w-8 h-8' : 'w-6 h-6'} rounded bg-gray-300`} />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
});

export default OptimizedSkeletonGrid;