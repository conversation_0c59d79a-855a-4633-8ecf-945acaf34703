"use client";

import ImageCard from './ImageCard';
import { useResponsive } from '@/hooks/useResponsive';

interface Image {
  id: number;
  image_url: string;
  aspect_ratio?: string;
  created_at: string;
  prompt?: string;
}

interface ImageGridProps {
  images: Image[];
  selectedIds: number[];
  isSelectMode: boolean;
  onImageSelected: (id: number) => void;
  onImageDeleted: (id: number) => void;
}

export default function ImageGrid({
  images,
  selectedIds,
  isSelectMode,
  onImageSelected,
  onImageDeleted
}: ImageGridProps) {
  const { isMobile, isTablet } = useResponsive();
  
  console.log("[ImageGrid] Rendering with", images.length, "images, isSelectMode:", isSelectMode);
  
  // Dynamic grid columns based on screen size
  const getGridCols = () => {
    if (isMobile) return 'grid-cols-1 sm:grid-cols-2';
    if (isTablet) return 'grid-cols-2 md:grid-cols-3';
    return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
  };
  
  // Dynamic gap based on screen size
  const getGridGap = () => {
    if (isMobile) return 'gap-4';
    return 'gap-6';
  };
  
  return (
    <div className={`grid ${getGridCols()} ${getGridGap()}`}>
      {images.map((image) => (
        <div
          key={image.id}
          onClick={() => isSelectMode && onImageSelected(image.id)}
          className={`${isSelectMode ? 'cursor-pointer' : ''} ${isMobile ? 'touch-manipulation' : ''}`}
        >
          <ImageCard
            id={image.id}
            imageUrl={image.image_url}
            createdAt={image.created_at}
            prompt={image.prompt}
            isSelectMode={isSelectMode}
            isSelected={selectedIds.includes(image.id)}
            onDelete={onImageDeleted}
          />
        </div>
      ))}
    </div>
  );
}
