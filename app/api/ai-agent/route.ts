import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { OpenAIAgent } from "@/lib/ai-agent/openai-agent";

export async function POST(request: Request) {
  try {
    console.log("[POST /api/ai-agent] Starting AI agent request...");

    const user = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    console.log("[POST /api/ai-agent] Request body:", {
      hasMessage: !!body.message,
      hasImages: !!body.images,
      isWelcome: !!body.isWelcome,
      hasFunctionCall: !!body.functionCall,
      hasConversationHistory: !!body.conversationHistory,
      hasUserContext: !!body.userContext,
      stream: !!body.stream
    });

    // Initialize OpenAI agent with user context
    const agent = new OpenAIAgent(body.userContext);

    // Check if streaming is requested
    if (body.stream) {
      return handleStreamingRequest(agent, body);
    }

    // Handle non-streaming requests (legacy support)
    let response;

    if (body.isWelcome) {
      // Generate welcome message
      console.log("[POST /api/ai-agent] Generating welcome message...");
      response = await agent.generateWelcomeMessage();
      
    } else if (body.functionCall) {
      // Direct function call (for testing or specific actions)
      console.log("[POST /api/ai-agent] Processing direct function call:", body.functionCall.name);
      
      // For now, return a message indicating function call processing
      response = {
        message: "I'm processing your request..."
      };
      
    } else if (body.message !== undefined) {
      // Regular conversation message
      console.log("[POST /api/ai-agent] Processing conversation message...");
      
      response = await agent.processMessage(
        body.message,
        body.images,
        body.conversationHistory || []
      );
      
    } else {
      return NextResponse.json(
        { error: "Invalid request format" },
        { status: 400 }
      );
    }

    console.log("[POST /api/ai-agent] Response generated:", {
      hasMessage: !!response.message,
      hasToolResults: !!(response as any).toolResults,
      hasGeneratedImages: !!(response as any).generatedImages,
      hasStructuredOutput: !!(response as any).structuredOutput
    });

    return NextResponse.json(response);

  } catch (error) {
    console.error("[POST /api/ai-agent] Error processing request:", error);
    
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "I'm sorry, I'm having trouble responding right now. Please try again in a moment."
      },
      { status: 500 }
    );
  }
}

/**
 * Handle streaming requests using Server-Sent Events
 */
async function handleStreamingRequest(agent: OpenAIAgent, body: any) {
  console.log("[POST /api/ai-agent] Handling streaming request...");

  // Create a ReadableStream for Server-Sent Events
  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      
      // Helper function to send SSE data with filtered logging
      const sendEvent = (eventType: string, data: any) => {
        // Add event type to data object for easier parsing
        const eventData = { ...data, type: eventType };
        const formattedData = `event: ${eventType}\ndata: ${JSON.stringify(eventData)}\n\n`;
        controller.enqueue(encoder.encode(formattedData));
        
        // Filtered logging - only log key events
        logSSEEvent(eventType, eventData);
      };

      // Filtered logging function for cleaner output
      const logSSEEvent = (eventType: string, data: any) => {
        switch (eventType) {
          case 'connected':
          case 'stream_end':
            console.log(`[SSE] ${eventType.toUpperCase()}: Stream lifecycle event`);
            break;
            
          case 'tool_call_start':
            console.log(`[SSE] TOOL_START: ${data.toolName} (${data.toolId})`);
            break;
            
          case 'tool_call_complete':
            console.log(`[SSE] TOOL_COMPLETE: ${data.toolName} -> ${data.state} (${data.toolId})`, {
              success: data.result?.success,
              hasImage: !!data.result?.image,
              hasError: !!data.result?.error
            });
            break;
            
          case 'message_complete':
            console.log(`[SSE] MESSAGE_COMPLETE: Response ready`, {
              hasMessage: !!data.message,
              hasToolResults: !!data.toolResults,
              hasImages: !!data.generatedImages,
              toolCount: data.toolResults?.length || 0,
              imageCount: data.generatedImages?.length || 0
            });
            break;
            
          case 'error':
            console.error(`[SSE] ERROR: ${data.error} (${data.type})`);
            break;
            
          case 'text_delta':
            // Skip individual text deltas - too verbose
            // Only log when text is complete or significant chunks
            if (data.accumulated && data.accumulated.length % 100 === 0) {
              console.log(`[SSE] TEXT_PROGRESS: ${data.accumulated.length} chars accumulated`);
            }
            break;
            
          case 'tool_call_progress':
            // Skip individual progress updates - too verbose
            // Only log significant milestones
            if (data.progress?.percentage && data.progress.percentage % 25 === 0) {
              console.log(`[SSE] TOOL_PROGRESS: ${data.toolName} -> ${data.progress.percentage}%`);
            }
            break;
            
          default:
            // Log unknown events with minimal data
            console.log(`[SSE] ${eventType.toUpperCase()}: Unknown event type`, {
              hasData: Object.keys(data).length > 1, // More than just 'type'
              dataKeys: Object.keys(data).filter(key => key !== 'type')
            });
        }
      };

      try {
        // Send connection established event
        sendEvent('connected', { message: 'Stream connected' });

        if (body.isWelcome) {
          // Handle welcome message streaming
          const response = await agent.generateWelcomeMessage();
          
          // Simulate streaming for welcome message
          const words = response.message.split(' ');
          let accumulatedText = '';
          
          for (let i = 0; i < words.length; i++) {
            accumulatedText += (i > 0 ? ' ' : '') + words[i];
            
            sendEvent('text_delta', {
              content: words[i] + (i < words.length - 1 ? ' ' : ''),
              accumulated: accumulatedText
            });
            
            // Small delay to simulate streaming
            await new Promise(resolve => setTimeout(resolve, 50));
          }
          
          sendEvent('message_complete', {
            message: response.message,
            type: 'welcome'
          });
          
        } else if (body.message !== undefined) {
          // Handle regular conversation with potential function calls
          
          await agent.processMessageStreaming(
            body.message,
            body.images,
            body.conversationHistory || [],
            {
              onTextDelta: (delta: string, accumulated: string) => {
                sendEvent('text_delta', {
                  content: delta,
                  accumulated: accumulated
                });
              },
              onToolCallStart: (toolName: string, toolId: string) => {
                sendEvent('tool_call_start', {
                  toolName,
                  toolId,
                  state: 'pending'
                });
              },
              onToolCallProgress: (toolName: string, toolId: string, progress: any) => {
                sendEvent('tool_call_progress', {
                  toolName,
                  toolId,
                  state: 'streaming',
                  progress
                });
              },
              onToolCallComplete: (toolName: string, toolId: string, result: any) => {
                sendEvent('tool_call_complete', {
                  toolName,
                  toolId,
                  state: result.success ? 'success' : 'error',
                  result
                });
              },
              onImagesReady: (images: any[], toolResults: any[]) => {
                sendEvent('images_ready', {
                  images,
                  toolResults,
                  timestamp: Date.now()
                });
              },
              onComplete: (finalResponse: any) => {
                sendEvent('message_complete', {
                  message: finalResponse.message,
                  toolResults: finalResponse.toolResults,
                  generatedImages: finalResponse.generatedImages,
                  structuredOutput: finalResponse.structuredOutput
                });
              },
              onError: (error: Error) => {
                sendEvent('error', {
                  error: error.message,
                  type: 'processing_error'
                });
              }
            }
          );
        } else {
          sendEvent('error', {
            error: 'Invalid request format',
            type: 'validation_error'
          });
        }

      } catch (error) {
        console.error('[SSE] Stream error:', error);
        sendEvent('error', {
          error: error instanceof Error ? error.message : 'Unknown error',
          type: 'stream_error'
        });
      } finally {
        // Close the stream
        sendEvent('stream_end', { message: 'Stream completed' });
        controller.close();
      }
    },
    
    cancel() {
      console.log('[SSE] Stream cancelled by client');
    }
  });

  // Return the streaming response with proper headers
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}

export async function GET() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}
