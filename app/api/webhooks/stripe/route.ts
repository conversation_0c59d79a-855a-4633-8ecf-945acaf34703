import { NextResponse } from "next/server";
import Strip<PERSON> from "stripe";
import { adminDb } from "@/lib/firebaseAdmin";
import { headers } from "next/headers";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  // @ts-expect-error: Type mismatch with new API versions
  apiVersion: "2023-10-16",
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Subscription plans configuration
const SUBSCRIPTION_PLANS = {
  basic: { credits: 50, price: 49 },
  growth: { credits: 230, price: 199 },
  pro: { credits: 480, price: 399 },
} as const;

type PlanId = keyof typeof SUBSCRIPTION_PLANS;

export async function POST(request: Request) {
  try {
    console.log("[POST /api/webhooks/stripe] Processing webhook");

    const body = await request.text();
    const headersList = await headers();
    const sig = headersList.get("stripe-signature");

    if (!sig || !endpointSecret) {
      console.error("[Webhook] Missing signature or endpoint secret");
      return NextResponse.json({ error: "Missing signature" }, { status: 400 });
    }

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
    } catch (err) {
      console.error(`[Webhook] Signature verification failed:`, err);
      return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
    }

    console.log(`[Webhook] Processing event: ${event.type}`);

    switch (event.type) {
      case "customer.subscription.created":
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        break;

      case "invoice.payment_succeeded":
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case "customer.subscription.updated":
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;

      case "customer.subscription.deleted":
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;

      case "invoice.payment_failed":
        await handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;

      default:
        console.log(`[Webhook] Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("[Webhook] Error processing webhook:", error);
    return NextResponse.json({ error: "Webhook processing failed" }, { status: 500 });
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log(`[Webhook] Subscription created: ${subscription.id}`);

  const customerId = subscription.customer as string;
  const userId = subscription.metadata?.userId;

  if (!userId) {
    console.error("[Webhook] No userId in subscription metadata");
    return;
  }

  // Extract plan information from subscription
  const priceId = subscription.items.data[0]?.price.id;
  const planId = getPlanIdFromPriceId(priceId);

  if (!planId) {
    console.error(`[Webhook] Could not determine plan from price ID: ${priceId}`);
    return;
  }

  const plan = SUBSCRIPTION_PLANS[planId];

  // Update user document with subscription info
  const subscriptionData = {
    subscription: {
      stripe_subscription_id: subscription.id,
      stripe_customer_id: customerId,
      status: subscription.status,
      plan_id: planId,
      monthly_credits: plan.credits,
      current_period_end: new Date((subscription as any).current_period_end * 1000),
      created_at: new Date(),
    },
  };

  await adminDb.collection("users").doc(userId).update(subscriptionData);

  console.log(`[Webhook] Subscription ${subscription.id} saved for user ${userId} with data:`, subscriptionData);
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log(`[Webhook] Payment succeeded: ${invoice.id}`);

  // Only process subscription invoices (not one-time payments)
  if (!(invoice as any).subscription) {
    console.log("[Webhook] Invoice is not for a subscription, skipping");
    return;
  }

  const subscriptionId = (invoice as any).subscription as string;
  console.log(`[Webhook] Processing subscription payment: ${subscriptionId}`);

  // Get subscription from Stripe to access metadata
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  const userId = subscription.metadata?.userId;

  if (!userId) {
    console.error(`[Webhook] No userId found in subscription metadata for ${subscriptionId}`);
    return;
  }

  console.log(`[Webhook] Found user ID from Stripe metadata: ${userId}`);

  // Get user document directly by ID
  const userDoc = await adminDb.collection("users").doc(userId).get();
  
  if (!userDoc.exists) {
    console.error(`[Webhook] User document ${userId} does not exist`);
    return;
  }

  const userData = userDoc.data();

  if (!userData) {
    console.error(`[Webhook] User document ${userId} has no data`);
    return;
  }

  if (!userData.subscription) {
    console.error(`[Webhook] User ${userId} has no subscription data`);
    return;
  }

  const creditsToAdd = userData.subscription.monthly_credits;

  // Add credits to user balance and update subscription status
  await adminDb.runTransaction(async (transaction) => {
    const userRef = adminDb.collection("users").doc(userId);
    const userSnap = await transaction.get(userRef);

    if (!userSnap.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const currentCredits = userSnap.data()?.credits || 0;
    const newBalance = currentCredits + creditsToAdd;

    transaction.update(userRef, {
      credits: newBalance,
      "subscription.status": "active", // Mark subscription as active when payment succeeds
    });

    console.log(
      `[Webhook] Payment succeeded! Added ${creditsToAdd} credits to user ${userId}, new balance: ${newBalance}`
    );
    console.log(`[Webhook] Updated subscription status to "active" for user ${userId}`);
  });

  // Log the credit addition for tracking
  await adminDb.collection("credit_transactions").add({
    user_id: userId,
    type: "subscription_credit",
    credits_added: creditsToAdd,
    stripe_invoice_id: invoice.id,
    stripe_subscription_id: subscriptionId,
    created_at: new Date(),
  });
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log(`[Webhook] Subscription updated: ${subscription.id}`);

  const userId = subscription.metadata?.userId;
  if (!userId) {
    console.error("[Webhook] No userId in subscription metadata");
    return;
  }

  // Check if subscription is being canceled at period end
  const isCanceledAtPeriodEnd = (subscription as any).cancel_at_period_end;
  const subscriptionStatus = isCanceledAtPeriodEnd ? "canceled" : subscription.status;

  console.log(`[Webhook] Subscription status: ${subscription.status}, cancel_at_period_end: ${isCanceledAtPeriodEnd}`);

  // Extract updated plan information
  const priceId = subscription.items.data[0]?.price.id;
  const planId = getPlanIdFromPriceId(priceId);

  if (!planId) {
    console.error(`[Webhook] Could not determine plan from price ID: ${priceId}`);
    return;
  }

  const plan = SUBSCRIPTION_PLANS[planId];

  // Update subscription info
  await adminDb.collection("users").doc(userId).update({
    "subscription.status": subscriptionStatus,
    "subscription.plan_id": planId,
    "subscription.monthly_credits": plan.credits,
    "subscription.current_period_end": new Date((subscription as any).current_period_end * 1000),
  });

  if (isCanceledAtPeriodEnd) {
    console.log(`[Webhook] Subscription ${subscription.id} marked as canceled (will end at period end) for user ${userId}`);
  } else {
    console.log(`[Webhook] Updated subscription ${subscription.id} for user ${userId} with status: ${subscriptionStatus}`);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log(`[Webhook] Subscription deleted: ${subscription.id}`);

  const userId = subscription.metadata?.userId;
  if (!userId) {
    console.error("[Webhook] No userId in subscription metadata");
    return;
  }

  // Mark subscription as canceled but keep the data
  await adminDb.collection("users").doc(userId).update({
    "subscription.status": "canceled",
  });

  console.log(`[Webhook] Marked subscription ${subscription.id} as canceled for user ${userId}`);
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  console.log(`[Webhook] Payment failed: ${invoice.id}`);

  if (!(invoice as any).subscription) {
    return;
  }

  const subscriptionId = (invoice as any).subscription as string;

  // Find user and update subscription status
  const userSnapshot = await adminDb
    .collection("users")
    .where("subscription.stripe_subscription_id", "==", subscriptionId)
    .get();

  if (!userSnapshot.empty) {
    const userDoc = userSnapshot.docs[0];
    await userDoc.ref.update({
      "subscription.status": "past_due",
    });

    console.log(`[Webhook] Marked subscription ${subscriptionId} as past_due`);
  }
}

function getPlanIdFromPriceId(priceId: string | undefined): PlanId | null {
  // This function maps Stripe price IDs to your plan IDs
  // You'll need to update this mapping when you create the Stripe products
  const priceMapping: Record<string, PlanId> = {
    // These will be set from environment variables once you create the Stripe products
    [process.env.STRIPE_BASIC_PRICE_ID || ""]: "basic",
    [process.env.STRIPE_GROWTH_PRICE_ID || ""]: "growth",
    [process.env.STRIPE_PRO_PRICE_ID || ""]: "pro",
  };

  return priceId ? priceMapping[priceId] || null : null;
}
