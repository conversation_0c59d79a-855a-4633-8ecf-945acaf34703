import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { getAdminDb } from "@/lib/firebaseAdmin";
import { Timestamp } from "firebase-admin/firestore";

export async function POST(request: Request) {
  try {
    console.log("[POST /api/memories] Saving user memory...");

    const user = await currentUser();
    if (!user) {
      console.log("[POST /api/memories] No Clerk user found. Unauthorized.");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { content, category, context, user_id } = body;

    console.log("[POST /api/memories] Request body:", {
      hasContent: !!content,
      category,
      hasContext: !!context,
      userId: user_id || user.id
    });

    // Validate required fields
    if (!content || content.trim().length === 0) {
      console.log("[POST /api/memories] Missing or empty content");
      return NextResponse.json({ error: "Content is required" }, { status: 400 });
    }

    // Ensure content is not too long
    if (content.length > 200) {
      console.log("[POST /api/memories] Content too long:", content.length);
      return NextResponse.json({ error: "Content must be 200 characters or less" }, { status: 400 });
    }

    const adminDb = getAdminDb();
    const clerkId = user_id || user.id;

    // Verify user exists
    const userDocRef = adminDb.collection("users").doc(clerkId);
    const userSnap = await userDocRef.get();
    
    if (!userSnap.exists) {
      console.log("[POST /api/memories] User document not found:", clerkId);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Create memory document
    const memoryData = {
      user_id: clerkId,
      content: content.trim(),
      category: category || 'general',
      context: context?.trim() || '',
      created_at: Timestamp.now(),
      updated_at: Timestamp.now()
    };

    console.log("[POST /api/memories] Creating memory document:", {
      userId: clerkId,
      contentLength: memoryData.content.length,
      category: memoryData.category,
      hasContext: !!memoryData.context
    });

    const memoryRef = await adminDb.collection("memories").add(memoryData);
    
    console.log("[POST /api/memories] Memory saved successfully:", memoryRef.id);

    return NextResponse.json({
      success: true,
      id: memoryRef.id,
      message: "Memory saved successfully"
    });

  } catch (error) {
    console.error("[POST /api/memories] Error saving memory:", error);
    
    return NextResponse.json(
      {
        error: "Failed to save memory",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    console.log("[GET /api/memories] Searching user memories...");

    const user = await currentUser();
    if (!user) {
      console.log("[GET /api/memories] No Clerk user found. Unauthorized.");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query') || '';
    const category = searchParams.get('category');
    const limit = parseInt(searchParams.get('limit') || '5');

    console.log("[GET /api/memories] Search parameters:", {
      query,
      category,
      limit,
      userId: user.id
    });

    const adminDb = getAdminDb();
    const clerkId = user.id;

    // Build query
    let memoriesQuery = adminDb
      .collection("memories")
      .where("user_id", "==", clerkId);

    // Add category filter if specified
    if (category && category !== 'null') {
      memoriesQuery = memoriesQuery.where("category", "==", category);
    }

    // Order by creation date (most recent first) and limit
    memoriesQuery = memoriesQuery
      .orderBy("created_at", "desc")
      .limit(Math.min(limit, 20)); // Max 20 results

    console.log("[GET /api/memories] Executing Firestore query...");
    const memoriesSnap = await memoriesQuery.get();
    
    console.log("[GET /api/memories] Found", memoriesSnap.size, "memories before filtering");

    // Convert to plain objects and filter by query if provided
    let memories = memoriesSnap.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        content: data.content || '',
        category: data.category || 'general',
        context: data.context || '',
        createdAt: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString()
      };
    });

    // Apply text search filter if query provided
    if (query && query.trim()) {
      const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
      
      memories = memories.filter(memory => {
        const searchableText = (
          memory.content + ' ' + 
          memory.category + ' ' + 
          (memory.context || '')
        ).toLowerCase();
        
        // Memory matches if it contains any of the search terms
        return searchTerms.some(term => searchableText.includes(term));
      });
    }

    console.log("[GET /api/memories] Filtered to", memories.length, "matching memories");

    return NextResponse.json({
      success: true,
      memories,
      total: memories.length,
      query,
      category
    });

  } catch (error) {
    console.error("[GET /api/memories] Error searching memories:", error);
    
    return NextResponse.json(
      {
        error: "Failed to search memories",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    console.log("[DELETE /api/memories] Deleting user memory...");

    const user = await currentUser();
    if (!user) {
      console.log("[DELETE /api/memories] No Clerk user found. Unauthorized.");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const memoryId = searchParams.get('id');

    if (!memoryId) {
      console.log("[DELETE /api/memories] Missing memory ID");
      return NextResponse.json({ error: "Memory ID is required" }, { status: 400 });
    }

    console.log("[DELETE /api/memories] Deleting memory:", memoryId, "for user:", user.id);

    const adminDb = getAdminDb();
    const clerkId = user.id;

    // Verify memory exists and belongs to user
    const memoryRef = adminDb.collection("memories").doc(memoryId);
    const memorySnap = await memoryRef.get();

    if (!memorySnap.exists) {
      console.log("[DELETE /api/memories] Memory not found:", memoryId);
      return NextResponse.json({ error: "Memory not found" }, { status: 404 });
    }

    const memoryData = memorySnap.data();
    if (memoryData?.user_id !== clerkId) {
      console.log("[DELETE /api/memories] Memory does not belong to user:", {
        memoryUserId: memoryData?.user_id,
        requestUserId: clerkId
      });
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Delete the memory
    await memoryRef.delete();
    
    console.log("[DELETE /api/memories] Memory deleted successfully:", memoryId);

    return NextResponse.json({
      success: true,
      message: "Memory deleted successfully"
    });

  } catch (error) {
    console.error("[DELETE /api/memories] Error deleting memory:", error);
    
    return NextResponse.json(
      {
        error: "Failed to delete memory",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}