import { getAdminDb } from "@/lib/firebaseAdmin";
import { FieldValue } from "firebase-admin/firestore";

/**
 * Helper functions for credit management using the Admin SDK (server-side).
 */

export async function checkCredits(userId: string, requiredCredits: number): Promise<boolean> {
  try {
    console.log(`[checkCredits] Checking if user ${userId} has ${requiredCredits} credits`);
    
    const adminDb = getAdminDb();
    const userDocRef = adminDb.collection("users").doc(userId);
    const userSnap = await userDocRef.get();

    if (!userSnap.exists) {
      console.log(`[checkCredits] User ${userId} not found in Firestore`);
      return false;
    }

    const userData = userSnap.data() || {};
    const currentCredits = userData.credits || 0;
    const hasEnoughCredits = currentCredits >= requiredCredits;

    console.log(
      `[checkCredits] User has ${currentCredits} credits, needs ${requiredCredits}, result: ${hasEnoughCredits}`
    );
    return hasEnoughCredits;
  } catch (err) {
    console.error("[checkCredits] Error checking credits:", err);
    return false;
  }
}

export async function deductCredits(userId: string, creditsToDeduct: number): Promise<boolean> {
  try {
    console.log(`[deductCredits] Deducting ${creditsToDeduct} credits from user ${userId}`);

    const adminDb = getAdminDb();
    
    // Use a transaction for an atomic operation
    await adminDb.runTransaction(async (transaction) => {
      const userDocRef = adminDb.collection("users").doc(userId);
      const userSnap = await transaction.get(userDocRef);

      if (!userSnap.exists) {
        throw new Error(`[deductCredits] User document not found for user ${userId}`);
      }

      const userData = userSnap.data() || {};
      const currentCredits = userData.credits || 0;

      if (currentCredits < creditsToDeduct) {
        throw new Error(`[deductCredits] User ${userId} doesn't have enough credits`);
      }

      transaction.update(userDocRef, {
        credits: FieldValue.increment(-creditsToDeduct),
      });
    });

    console.log(`[deductCredits] Successfully deducted ${creditsToDeduct} credits from user ${userId}`);
    return true;
  } catch (err) {
    console.error("[deductCredits] Error deducting credits:", err);
    return false;
  }
}
