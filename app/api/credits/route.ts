import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { checkCredits, deductCredits } from "./utils";
import { getAdminDb } from "@/lib/firebaseAdmin";

/**
 * "credits" route: GET fetches user credits, POST performs credit operations (check, deduct).
 */

const PLANS = [
  {
    id: "basic",
    name: "Basic",
    price: 49,
    credits: 50,
    costPerCredit: 0.98
  },
  {
    id: "base",
    name: "Base",
    price: 99,
    credits: 110,
    costPerCredit: 0.90
  },
  {
    id: "growth",
    name: "Growth",
    price: 199,
    credits: 230,
    costPerCredit: 0.87,
    mostPopular: true
  },
  {
    id: "pro",
    name: "Pro",
    price: 399,
    credits: 480,
    costPerCredit: 0.83
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: 999,
    credits: 1300,
    costPerCredit: 0.77
  }
];

export async function GET() {
  try {
    console.log("[GET /api/credits] Fetching credit information");

    const user = await currentUser();
    if (!user) {
      console.log("[GET /api/credits] No user found, unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
      const adminDb = getAdminDb();
      const clerkId = user.id;
      
      // First, check if user exists - if not, create it with 0 credits
      const userRef = adminDb.collection("users").doc(clerkId);
      const userSnap = await userRef.get();

      let currentCredits = 0;
      
      if (!userSnap.exists) {
        // User doesn't exist, create it
        console.log(`[GET /api/credits] User ${clerkId} not found. Creating user document...`);
        await userRef.set({
          email: user.emailAddresses[0]?.emailAddress || null,
          credits: 0,
          created_at: new Date().toISOString(),
          last_login: new Date().toISOString(),
        });
      } else {
        // User exists, get credits
        const userData = userSnap.data() || {};
        currentCredits = userData.credits || 0;
      }

      console.log(`[GET /api/credits] User ${clerkId} has ${currentCredits} credits`);

      return NextResponse.json({
        credits: currentCredits,
        plans: PLANS,
      });
    } catch (dbError: unknown) {
      console.error("[GET /api/credits] Database error:", dbError);
      return NextResponse.json(
        { 
          error: "Failed to fetch credit information from database",
          details: dbError instanceof Error ? dbError.message : String(dbError)
        },
        { status: 500 }
      );
    }
  } catch (err: unknown) {
    console.error("[GET /api/credits] Error:", err);
    return NextResponse.json(
      { 
        error: "Failed to fetch credit information",
        details: err instanceof Error ? err.message : String(err)
      },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    console.log("[POST /api/credits] Processing credit operation");

    const user = await currentUser();
    if (!user) {
      console.log("[POST /api/credits] No user found, unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clerkId = user.id;
    const body = await request.json();
    const { operation, amount } = body;

    if (!operation || !amount || typeof amount !== "number" || amount <= 0) {
      return NextResponse.json({ error: "Invalid request parameters" }, { status: 400 });
    }

    try {
      // Update the utils to use getAdminDb()
      if (operation === "check") {
        const hasCredits = await checkCredits(clerkId, amount);
        return NextResponse.json({ hasCredits });
      } else if (operation === "deduct") {
        const success = await deductCredits(clerkId, amount);
        if (success) {
          return NextResponse.json({
            success: true,
            message: `Successfully deducted ${amount} credits`,
          });
        } else {
          return NextResponse.json(
            { success: false, error: "Failed to deduct credits" },
            { status: 400 }
          );
        }
      } else {
        return NextResponse.json({ error: "Invalid operation" }, { status: 400 });
      }
    } catch (dbError: unknown) {
      console.error("[POST /api/credits] Database error:", dbError);
      return NextResponse.json(
        { 
          error: "Failed to process credit operation in database",
          details: dbError instanceof Error ? dbError.message : String(dbError)
        },
        { status: 500 }
      );
    }
  } catch (err: unknown) {
    console.error("[POST /api/credits] Error:", err);
    return NextResponse.json(
      { 
        error: "Failed to process credit operation",
        details: err instanceof Error ? err.message : String(err)
      },
      { status: 500 }
    );
  }
}