import { NextRequest, NextResponse } from 'next/server';
import { ImageConverter } from '@/lib/server/image-converter';

export async function POST(request: NextRequest) {
  try {
    console.log('[API] Image conversion request received');
    
    const body = await request.json();
    const { imageData, targetFormat = 'jpeg' } = body;
    
    if (!imageData) {
      return NextResponse.json(
        { success: false, error: 'Missing imageData parameter' },
        { status: 400 }
      );
    }
    
    console.log('[API] Converting image with target format:', targetFormat);
    
    const result = await ImageConverter.convertToOpenAIFormat(imageData, targetFormat);
    
    if (!result.success) {
      console.error('[API] Image conversion failed:', result.error);
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
    
    console.log('[API] Image conversion successful:', {
      originalFormat: result.originalFormat,
      convertedFormat: result.convertedFormat
    });
    
    return NextResponse.json({
      success: true,
      dataUrl: result.dataUrl,
      originalFormat: result.originalFormat,
      convertedFormat: result.convertedFormat
    });
    
  } catch (error) {
    console.error('[API] Image conversion error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown server error' 
      },
      { status: 500 }
    );
  }
}