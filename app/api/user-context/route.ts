import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { getAdminDb } from "@/lib/firebaseAdmin";

export async function GET() {
  try {
    console.log("[GET /api/user-context] Fetching user context...");

    const user = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clerkId = user.id;
    const adminDb = getAdminDb();

    // Get user data from Firestore
    const userDocRef = adminDb.collection("users").doc(clerkId);
    const userSnap = await userDocRef.get();
    
    if (!userSnap.exists) {
      // User doesn't exist in database, create with default values
      const defaultUserData = {
        clerk_id: clerkId,
        email: user.emailAddresses[0]?.emailAddress || '',
        credits: 0,
        created_at: new Date(),
        updated_at: new Date()
      };
      
      await userDocRef.set(defaultUserData);
      
      console.log("[GET /api/user-context] Created new user record for:", clerkId);
      
      return NextResponse.json({
        id: clerkId,
        email: defaultUserData.email,
        name: user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : undefined,
        credits: 0,
        subscription: null,
        recentImages: []
      });
    }

    const userData = userSnap.data() || {};
    
    // Get recent images (last 5)
    const recentImagesQuery = adminDb
      .collection("images")
      .where("user_id", "==", clerkId)
      .orderBy("created_at", "desc")
      .limit(5);
    
    const recentImagesSnap = await recentImagesQuery.get();
    const recentImages = recentImagesSnap.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        url: data.image_url,
        createdAt: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        prompt: data.prompt
      };
    });

    // Build user context
    const userContext = {
      id: clerkId,
      email: userData.email || user.emailAddresses[0]?.emailAddress || '',
      name: user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : undefined,
      credits: userData.credits || 0,
      subscription: userData.subscription_status ? {
        status: userData.subscription_status,
        plan: userData.subscription_plan || 'free'
      } : null,
      recentImages
    };

    console.log("[GET /api/user-context] User context retrieved:", {
      id: userContext.id,
      hasName: !!userContext.name,
      credits: userContext.credits,
      hasSubscription: !!userContext.subscription,
      recentImagesCount: recentImages.length
    });

    return NextResponse.json(userContext);

  } catch (error) {
    console.error("[GET /api/user-context] Error fetching user context:", error);
    
    return NextResponse.json(
      { error: "Failed to fetch user context" },
      { status: 500 }
    );
  }
}

export async function POST() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}