import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { getAdminDb } from "@/lib/firebaseAdmin";

/**
 * "users" route: stores user in Firestore and fetches user data.
 */

export async function POST() {
  try {
    console.log("[POST /api/users] Endpoint triggered");

    const user = await currentUser();
    if (!user) {
      console.log("[POST /api/users] No Clerk user found in request context. Unauthorized.");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const adminDbInstance = getAdminDb();
    const clerkId = user.id;
    const email = user.emailAddresses[0]?.emailAddress || null;

    console.log(`[POST /api/users] Clerk ID: ${clerkId}`);
    console.log(`[POST /api/users] Email: ${email}`);

    // Check if the user document already exists
    const userDocRef = adminDbInstance.collection("users").doc(clerkId);
    const userSnap = await userDocRef.get();

    if (!userSnap.exists) {
      // If doc doesn't exist, create it with 0 credits
      console.log(`[POST /api/users] User doc does not exist. Creating new doc with 0 credits.`);
      await userDocRef.set({
        email: email,
        credits: 0,
        created_at: new Date().toISOString(),
        last_login: new Date().toISOString(),
      });
      console.log(`[POST /api/users] Created new user document for ${clerkId} with 0 credits.`);
    } else {
      // If doc exists, just update last_login (preserving existing credits)
      console.log(`[POST /api/users] User doc exists. Updating last_login only, preserving credits.`);
      await userDocRef.update({
        email: email,
        last_login: new Date().toISOString(),
      });
      console.log(`[POST /api/users] Updated existing user document for ${clerkId}, credits preserved.`);
    }

    return NextResponse.json({ success: true });
  } catch (err: unknown) {
    console.error("[POST /api/users] Error:", err);
    return NextResponse.json(
      {
        error: "Server error",
        details: err instanceof Error ? err.message : String(err),
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    console.log("[GET /api/users] Endpoint triggered");

    const user = await currentUser();
    if (!user) {
      console.log("[GET /api/users] No Clerk user found in request context. Unauthorized.");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const adminDbInstance = getAdminDb();
    const clerkId = user.id;
    console.log(`[GET /api/users] Clerk ID: ${clerkId}`);

    // Attempt to get the user document
    const userDocRef = adminDbInstance.collection("users").doc(clerkId);
    const userSnap = await userDocRef.get();

    if (!userSnap.exists) {
      // If user doesn't exist in Firestore yet, create it on the fly with 0 credits
      console.log(`[GET /api/users] User doc not found. Creating new doc with 0 credits.`);
      await userDocRef.set({
        email: user.emailAddresses[0]?.emailAddress || null,
        credits: 0,
        created_at: new Date().toISOString(),
        last_login: new Date().toISOString(),
      });

      // Return the newly created user data
      return NextResponse.json({
        email: user.emailAddresses[0]?.emailAddress || null,
        credits: 0,
        created_at: new Date().toISOString(),
      });
    }

    // If user doc exists, return its data
    const userData = userSnap.data() || {};
    const email = userData.email || null;
    const credits = userData.credits || 0;
    const created_at = userData.created_at || null;

    console.log(`[GET /api/users] Fetched user data: ${JSON.stringify(userData)}`);

    return NextResponse.json({
      email: email,
      credits: credits,
      created_at: created_at,
    });
  } catch (err: unknown) {
    console.error("[GET /api/users] Error:", err);
    return NextResponse.json(
      {
        error: "Server error",
        details: err instanceof Error ? err.message : String(err),
      },
      { status: 500 }
    );
  }
}
