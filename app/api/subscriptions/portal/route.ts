import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import <PERSON><PERSON> from "stripe";
import { adminDb } from "@/lib/firebaseAdmin";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  // @ts-expect-error: Type mismatch with new API versions
  apiVersion: "2023-10-16",
});

export async function POST(request: Request) {
  try {
    console.log("[POST /api/subscriptions/portal] Creating customer portal session");

    const user = await currentUser();
    if (!user) {
      console.log("[POST /api/subscriptions/portal] No user found, unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's Stripe customer ID from Firestore
    const userDoc = await adminDb.collection("users").doc(user.id).get();
    const userData = userDoc.data();

    if (!userData?.subscription?.stripe_customer_id) {
      console.log("[POST /api/subscriptions/portal] No Stripe customer ID found");
      return NextResponse.json({ error: "No subscription found" }, { status: 400 });
    }

    const customerId = userData.subscription.stripe_customer_id;

    // Get the return URL
    const origin = request.headers.get("origin") || process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const returnUrl = `${origin}/settings`;

    // Create customer portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });

    console.log(`[POST /api/subscriptions/portal] Created portal session: ${portalSession.id}`);

    return NextResponse.json({ url: portalSession.url });
  } catch (error) {
    console.error("[POST /api/subscriptions/portal] Error creating portal session:", error);
    return NextResponse.json({ error: "Failed to create portal session" }, { status: 500 });
  }
}
