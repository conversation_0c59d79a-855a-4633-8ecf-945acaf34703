import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import Strip<PERSON> from "stripe";
import { adminDb } from "@/lib/firebaseAdmin";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  // @ts-expect-error: Type mismatch with new API versions
  apiVersion: "2023-10-16",
});

// Subscription plans configuration - these will map to your Stripe price IDs
const SUBSCRIPTION_PLANS = {
  basic: {
    credits: 50,
    price: 49,
    stripe_price_id: process.env.STRIPE_BASIC_PRICE_ID,
  },
  growth: {
    credits: 230,
    price: 199,
    stripe_price_id: process.env.STRIPE_GROWTH_PRICE_ID,
  },
  pro: {
    credits: 480,
    price: 399,
    stripe_price_id: process.env.STRIPE_PRO_PRICE_ID,
  },
} as const;

type PlanId = keyof typeof SUBSCRIPTION_PLANS;

export async function POST(request: Request) {
  try {
    console.log("[POST /api/subscriptions/checkout] Creating subscription checkout session");

    const user = await currentUser();
    if (!user) {
      console.log("[POST /api/subscriptions/checkout] No user found, unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const planId = body.planId as PlanId;

    // Validate the planId
    if (!planId || !(planId in SUBSCRIPTION_PLANS)) {
      console.log(`[POST /api/subscriptions/checkout] Invalid plan ID: ${planId}`);
      return NextResponse.json({ error: "Invalid plan ID" }, { status: 400 });
    }

    const plan = SUBSCRIPTION_PLANS[planId];
    
    if (!plan.stripe_price_id) {
      console.error(`[POST /api/subscriptions/checkout] No Stripe price ID for plan: ${planId}`);
      return NextResponse.json({ error: "Plan configuration error" }, { status: 500 });
    }

    console.log(`[POST /api/subscriptions/checkout] plan: ${planId}, price: $${plan.price}, credits: ${plan.credits}/month`);

    // Check if user already has an active subscription
    const userDoc = await adminDb.collection("users").doc(user.id).get();
    const userData = userDoc.data();

    if (userData?.subscription?.status === "active") {
      console.log("[POST /api/subscriptions/checkout] User already has active subscription");
      return NextResponse.json({ error: "User already has an active subscription" }, { status: 400 });
    }

    // success/cancel URLs
    const origin = request.headers.get("origin") || process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const successUrl = `${origin}/settings?subscription_success=true&plan=${planId}&session_id={CHECKOUT_SESSION_ID}`;
    const cancelUrl = `${origin}/settings?subscription_canceled=true`;

    // Get or create Stripe customer
    let customerId = userData?.subscription?.stripe_customer_id;

    if (!customerId) {
      console.log("[POST /api/subscriptions/checkout] Creating new Stripe customer");
      const customer = await stripe.customers.create({
        email: user.emailAddresses[0]?.emailAddress,
        metadata: {
          userId: user.id,
          clerkId: user.id,
        },
      });
      customerId = customer.id;
    }

    // Create Stripe checkout session for subscription
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: [
        {
          price: plan.stripe_price_id,
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: successUrl,
      cancel_url: cancelUrl,
      subscription_data: {
        metadata: {
          userId: user.id,
          planId,
        },
      },
      metadata: {
        userId: user.id,
        planId,
      },
    });

    console.log(`[POST /api/subscriptions/checkout] session.id=${session.id}, session.url=${session.url}`);

    return NextResponse.json({ url: session.url });
  } catch (err) {
    console.error("[POST /api/subscriptions/checkout] Error creating subscription checkout:", err);
    return NextResponse.json({ error: "Failed to create subscription checkout" }, { status: 500 });
  }
}

// Get subscription status for a user
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: Request) {
  try {
    const user = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userDoc = await adminDb.collection("users").doc(user.id).get();
    const userData = userDoc.data();

    if (!userData?.subscription) {
      return NextResponse.json({ 
        hasSubscription: false,
        credits: userData?.credits || 0 
      });
    }

    const subscription = userData.subscription;
    
    return NextResponse.json({
      hasSubscription: true,
      subscription: {
        status: subscription.status,
        plan_id: subscription.plan_id,
        monthly_credits: subscription.monthly_credits,
        current_period_end: subscription.current_period_end?.toDate ? 
          subscription.current_period_end.toDate().toISOString() : 
          subscription.current_period_end,
      },
      credits: userData.credits || 0,
    });
  } catch (error) {
    console.error("[GET /api/subscriptions/checkout] Error fetching subscription:", error);
    return NextResponse.json({ error: "Failed to fetch subscription" }, { status: 500 });
  }
}
