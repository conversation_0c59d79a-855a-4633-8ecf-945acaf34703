import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import <PERSON><PERSON> from "stripe";
import { adminDb } from "@/lib/firebaseAdmin";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  // @ts-expect-error: Type mismatch with new API versions
  apiVersion: "2023-10-16",
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function POST(_request: Request) {
  try {
    console.log("[POST /api/checkout] DEPRECATED: One-time checkout disabled, redirecting to subscriptions");

    return NextResponse.json({ 
      error: "One-time purchases are no longer available. Please use our subscription plans instead.",
      redirect: "/settings"
    }, { status: 400 });
  } catch (err) {
    console.error("[POST /api/checkout] Error:", err);
    return NextResponse.json({ error: "Checkout not available" }, { status: 500 });
  }
}

export async function GET(request: Request) {
  try {
    console.log("[GET /api/checkout] Processing checkout success");

    const url = new URL(request.url);
    const sessionId = url.searchParams.get("session_id");
    console.log("[GET /api/checkout] session_id:", sessionId);

    const user = await currentUser();
    if (!user) {
      console.log("[GET /api/checkout] No user found, unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!sessionId) {
      console.log("[GET /api/checkout] No session ID provided");
      return NextResponse.json({ error: "No session ID provided" }, { status: 400 });
    }

    const session = await stripe.checkout.sessions.retrieve(sessionId);
    console.log(`[GET /api/checkout] Stripe session payment_status=${session.payment_status}`);

    if (session.payment_status !== "paid") {
      console.log(`[GET /api/checkout] Payment not completed: ${session.payment_status}`);
      return NextResponse.json({ error: "Payment not completed" }, { status: 400 });
    }

    const credits = session.metadata?.credits ? parseInt(session.metadata.credits) : 0;

    // Check if we already have a "paid" transaction for this session
    const paidTxSnapshot = await adminDb
      .collection("transactions")
      .where("stripe_session_id", "==", sessionId)
      .where("stripe_payment_status", "==", "paid")
      .get();

    if (!paidTxSnapshot.empty) {
      console.log("[GET /api/checkout] Transaction already processed");
      return NextResponse.json({
        success: true,
        credits,
        total: session.amount_total ? session.amount_total / 100 : 0,
        message: "Transaction was already processed",
      });
    }

    // Find the transaction doc for this session
    const pendingTxSnapshot = await adminDb
      .collection("transactions")
      .where("stripe_session_id", "==", sessionId)
      .get();

    let transactionDocId: string | null = null;
    if (!pendingTxSnapshot.empty) {
      transactionDocId = pendingTxSnapshot.docs[0].id;
      console.log(`[GET /api/checkout] Found transaction doc: ${transactionDocId}`);
    }

    // Mark as "paid" or create new if missing
    if (transactionDocId) {
      await adminDb.collection("transactions").doc(transactionDocId).update({
        stripe_payment_status: "paid",
        updated_at: new Date(),
      });
    } else {
      console.log("[GET /api/checkout] No transaction doc found, creating new one");
      await adminDb.collection("transactions").add({
        user_id: user.id,
        amount: session.amount_total ? session.amount_total / 100 : 0,
        credits,
        stripe_session_id: sessionId,
        stripe_payment_status: "paid",
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    // Update user credits
    const newCreditBalance = await updateUserCredits(user.id, credits);
    console.log(
      `[GET /api/checkout] Added ${credits} credits to user ${user.id}, new balance: ${newCreditBalance}`
    );

    return NextResponse.json({
      success: true,
      credits,
      newBalance: newCreditBalance,
      total: session.amount_total ? session.amount_total / 100 : 0,
    });
  } catch (err) {
    console.error("[GET /api/checkout] Error processing checkout success:", err);
    return NextResponse.json({ error: "Failed to process checkout success" }, { status: 500 });
  }
}

async function updateUserCredits(userId: string, creditsToAdd: number): Promise<number> {
  console.log(`[updateUserCredits] Adding ${creditsToAdd} credits to user ${userId}`);

  // We'll do a transaction for safety
  let newBalance = 0;

  await adminDb.runTransaction(async (transaction) => {
    const userDocRef = adminDb.collection("users").doc(userId);
    const userSnap = await transaction.get(userDocRef);

    if (!userSnap.exists) {
      // If no user doc, create one
      newBalance = creditsToAdd;
      transaction.set(userDocRef, {
        credits: newBalance,
        created_at: new Date().toISOString(),
      });
    } else {
      const currentCredits = (userSnap.data() || {}).credits || 0;
      newBalance = currentCredits + creditsToAdd;
      transaction.update(userDocRef, { credits: newBalance });
    }
  });

  return newBalance;
}