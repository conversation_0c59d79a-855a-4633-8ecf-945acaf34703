import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { getAdminDb } from "@/lib/firebaseAdmin";
import { Timestamp, QueryDocumentSnapshot } from "firebase-admin/firestore";

/**
 * POST endpoint removed - Legacy pre-AI image generation pipeline has been cleaned up.
 * Image generation is now handled by the AI agent system at /api/ai-agent
 * which uses simplified function calling and /lib/imageGeneration-simple.ts
 */
export async function POST() {
  return NextResponse.json(
    {
      error: "This endpoint has been deprecated",
      message: "Image generation is now handled by the AI agent. Please use the AI chat interface instead.",
      redirect: "/create"
    },
    { status: 410 } // 410 Gone - indicates that the resource is no longer available
  );
}

// PERFORMANCE OPTIMIZED GET ENDPOINT
export async function GET(request: Request) {
  try {
    console.log("[GET /api/images] Fetching images...");

    const user = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clerkId = user.id;
    const url = new URL(request.url);
    let limit = parseInt(url.searchParams.get("limit") || "20", 10);
    const cursor = url.searchParams.get("cursor"); // For cursor-based pagination
    
    if (isNaN(limit) || limit < 1) limit = 20;
    if (limit > 100) limit = 100; // Cap the limit for performance

    const adminDb = getAdminDb();
    
    // Build base query
    let imagesQuery = adminDb
      .collection("images")
      .where("user_id", "==", clerkId)
      .orderBy("created_at", "desc")
      .limit(limit);

    // Use cursor-based pagination for better performance if cursor is provided
    if (cursor) {
      try {
        const cursorDoc = await adminDb.collection("images").doc(cursor).get();
        if (cursorDoc.exists) {
          imagesQuery = imagesQuery.startAfter(cursorDoc);
        }
      } catch {
        console.warn("[GET /api/images] Invalid cursor, falling back to offset pagination");
      }
    }

    const snapshot = await imagesQuery.get();
    const docs = snapshot.docs;

    const images = docs.map((docSnap: QueryDocumentSnapshot) => {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        user_id: data.user_id,
        image_url: data.image_url,
        input_image_reference: data.input_image_reference || data.input_image_url,
        prompt: data.prompt,
        aspect_ratio: data.aspect_ratio,
        parameters: data.parameters,
        created_at: (data.created_at as Timestamp)?.toDate().toISOString()
      };
    });

    // Determine next cursor for pagination
    const nextCursor = docs.length === limit ? docs[docs.length - 1].id : null;
    const hasMore = docs.length === limit;

    console.log(
      `[GET /api/images] Found ${images.length} images, hasMore: ${hasMore}, nextCursor: ${nextCursor}`
    );

    // Set cache headers for better performance
    const response = NextResponse.json({
      images,
      hasMore,
      nextCursor
    });

    // Add cache headers (5 minutes for image lists)
    response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=300');
    response.headers.set('ETag', `"${clerkId}-${cursor || 'initial'}"`);

    return response;
  } catch (err: unknown) {
    console.error("[GET /api/images] Error fetching images:", err);
    return NextResponse.json(
      {
        error: `Failed to fetch images: ${
          err instanceof Error ? err.message : String(err)
        }`
      },
      { status: 500 }
    );
  }
}