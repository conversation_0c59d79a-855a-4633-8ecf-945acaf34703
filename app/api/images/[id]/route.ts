import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { getAdminDb } from "@/lib/firebaseAdmin";
import { deleteFileByUrl } from "@/lib/blobStorageUtils";

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    console.log(`[DELETE /api/images/${id}] Starting image deletion...`);

    const user = await currentUser();
    if (!user) {
      console.log(`[DELETE /api/images/${id}] Unauthorized access attempt.`);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clerkId = user.id;
    if (!id) {
      console.log(`[DELETE /api/images/${id}] Invalid image ID.`);
      return NextResponse.json({ error: "Invalid image ID" }, { status: 400 });
    }

    // Fetch the image doc
    const adminDb = getAdminDb();
    const imageDocRef = adminDb.collection("images").doc(id);
    const imageSnap = await imageDocRef.get();

    if (!imageSnap.exists) {
      console.log(`[DELETE /api/images/${id}] Image not found in Firestore.`);
      return NextResponse.json({ error: "Image not found" }, { status: 404 });
    }

    const imageData = imageSnap.data() || {};
    if (imageData.user_id !== clerkId) {
      console.log(`[DELETE /api/images/${id}] User does not own this image.`);
      return NextResponse.json({ error: "Image not found" }, { status: 404 });
    }

    // Delete the image file from Vercel Blob if URL exists
    if (imageData.image_url) {
      try {
        await deleteFileByUrl(imageData.image_url);
        console.log(`[DELETE /api/images/${id}] Deleted image file from Vercel Blob.`);
      } catch (storageErr) {
        console.error(`[DELETE /api/images/${id}] Error deleting from Vercel Blob:`, storageErr);
        // Continue with Firestore deletion even if Blob deletion fails
      }
    }

    // Delete from Firestore
    await imageDocRef.delete();
    console.log(`[DELETE /api/images/${id}] Successfully deleted from Firestore.`);

    return NextResponse.json({ message: "Image deleted successfully" }, { status: 200 });
  } catch (err) {
    console.error(`[DELETE /api/images/${id}] Error:`, err);
    return NextResponse.json(
      { error: "Internal Server Error during image deletion" },
      { status: 500 }
    );
  }
}