import { useState, useEffect, useRef, useCallback } from 'react';

interface UseImageLazyLoadingOptions {
  rootMargin?: string;
  threshold?: number;
  enablePreload?: boolean;
  preloadCount?: number;
}

interface ImageLoadState {
  isLoading: boolean;
  isLoaded: boolean;
  error: string | null;
  shouldLoad: boolean;
}

export function useImageLazyLoading(
  src: string,
  options: UseImageLazyLoadingOptions = {}
) {
  const {
    rootMargin = '50px',
    threshold = 0.1,
    enablePreload = true,
    preloadCount = 3
  } = options;

  const [loadState, setLoadState] = useState<ImageLoadState>({
    isLoading: false,
    isLoaded: false,
    error: null,
    shouldLoad: false
  });

  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const loadImage = useCallback(() => {
    if (!src || loadState.isLoaded || loadState.isLoading) return;

    setLoadState(prev => ({ ...prev, isLoading: true, error: null }));

    const img = new Image();
    
    img.onload = () => {
      setLoadState(prev => ({
        ...prev,
        isLoading: false,
        isLoaded: true,
        error: null
      }));
    };

    img.onerror = () => {
      setLoadState(prev => ({
        ...prev,
        isLoading: false,
        isLoaded: false,
        error: 'Failed to load image'
      }));
    };

    img.src = src;
  }, [src, loadState.isLoaded, loadState.isLoading]);

  useEffect(() => {
    if (!imgRef.current) return;

    // Create intersection observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setLoadState(prev => ({ ...prev, shouldLoad: true }));
          loadImage();
          // Unobserve after loading starts
          if (observerRef.current && imgRef.current) {
            observerRef.current.unobserve(imgRef.current);
          }
        }
      },
      {
        rootMargin,
        threshold
      }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [rootMargin, threshold, loadImage]);

  // Preload image immediately if it's within the first few images
  useEffect(() => {
    if (enablePreload && !loadState.shouldLoad) {
      const container = imgRef.current?.closest('[data-image-index]');
      const index = container?.getAttribute('data-image-index');
      if (index && parseInt(index) < preloadCount) {
        setLoadState(prev => ({ ...prev, shouldLoad: true }));
        loadImage();
      }
    }
  }, [enablePreload, preloadCount, loadImage, loadState.shouldLoad]);

  return {
    ...loadState,
    imgRef
  };
}

export function useInfiniteScroll(
  fetchMore: () => Promise<void>,
  hasMore: boolean,
  isLoading: boolean,
  threshold = 200
) {
  const [isFetching, setIsFetching] = useState(false);
  const sentinelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!sentinelRef.current || !hasMore || isLoading || isFetching) return;

    const observer = new IntersectionObserver(
      async (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !isLoading && !isFetching) {
          setIsFetching(true);
          try {
            await fetchMore();
          } finally {
            setIsFetching(false);
          }
        }
      },
      {
        rootMargin: `${threshold}px`,
        threshold: 0.1
      }
    );

    observer.observe(sentinelRef.current);

    return () => observer.disconnect();
  }, [fetchMore, hasMore, isLoading, isFetching, threshold]);

  return {
    sentinelRef,
    isFetching
  };
}

// Hook for virtual scrolling (for large datasets)
export function useVirtualGrid(
  items: any[],
  containerHeight: number,
  itemHeight: number,
  itemsPerRow: number,
  overscan = 3
) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const totalRows = Math.ceil(items.length / itemsPerRow);
  const rowHeight = itemHeight;
  
  const startRow = Math.max(0, Math.floor(scrollTop / rowHeight) - overscan);
  const endRow = Math.min(
    totalRows - 1,
    Math.floor((scrollTop + containerHeight) / rowHeight) + overscan
  );

  const visibleItems = [];
  for (let row = startRow; row <= endRow; row++) {
    const startIndex = row * itemsPerRow;
    const endIndex = Math.min(startIndex + itemsPerRow, items.length);
    for (let i = startIndex; i < endIndex; i++) {
      visibleItems.push({
        index: i,
        item: items[i],
        style: {
          position: 'absolute' as const,
          top: Math.floor(i / itemsPerRow) * rowHeight,
          left: (i % itemsPerRow) * (100 / itemsPerRow) + '%',
          width: (100 / itemsPerRow) + '%',
          height: rowHeight
        }
      });
    }
  }

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    containerRef,
    visibleItems,
    totalHeight: totalRows * rowHeight,
    handleScroll
  };
}