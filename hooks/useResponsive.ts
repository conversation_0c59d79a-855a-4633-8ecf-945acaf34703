"use client";

import { useState, useEffect } from 'react';

// Consistent breakpoint system
export const breakpoints = {
  mobile: '(max-width: 767px)',      // Mobile phones
  tablet: '(min-width: 768px) and (max-width: 1023px)', // Tablets  
  desktop: '(min-width: 1024px)'     // Desktop
};

interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
}

export function useResponsive(): ResponsiveState {
  const [state, setState] = useState<ResponsiveState>({
    isMobile: false,
    isTablet: false,
    isDesktop: true, // Default to desktop for SSR
    screenWidth: 1024 // Default width for SSR
  });

  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined') return;

    const mobileQuery = window.matchMedia(breakpoints.mobile);
    const tabletQuery = window.matchMedia(breakpoints.tablet);
    const desktopQuery = window.matchMedia(breakpoints.desktop);

    const handleResize = () => {
      const screenWidth = window.innerWidth;
      setState({
        isMobile: mobileQuery.matches,
        isTablet: tabletQuery.matches,
        isDesktop: desktopQuery.matches,
        screenWidth
      });
    };

    // Initial check
    handleResize();

    // Add event listeners
    mobileQuery.addEventListener('change', handleResize);
    tabletQuery.addEventListener('change', handleResize);
    desktopQuery.addEventListener('change', handleResize);
    window.addEventListener('resize', handleResize);

    return () => {
      mobileQuery.removeEventListener('change', handleResize);
      tabletQuery.removeEventListener('change', handleResize);
      desktopQuery.removeEventListener('change', handleResize);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return state;
}

// Utility hook for simple mobile detection
export function useIsMobile(): boolean {
  const { isMobile } = useResponsive();
  return isMobile;
}

// Utility hook for desktop detection
export function useIsDesktop(): boolean {
  const { isDesktop } = useResponsive();
  return isDesktop;
}