import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

// Define protected routes - everything inside (dashboard) group
const isProtectedRoute = createRouteMatcher([
  '/create(.*)',
  '/library(.*)',
  '/settings(.*)',
]);

export default clerkMiddleware(async (auth, req) => {
  if (isProtectedRoute(req)) {
    await auth.protect();
  }
  
  // Optional: We could integrate a check here to call the user API route
  // to ensure the user exists in Firestore, but this is generally best
  // handled directly in the /api/users route when needed or on first sign-in
  // to avoid making too many requests on every page load
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}; 