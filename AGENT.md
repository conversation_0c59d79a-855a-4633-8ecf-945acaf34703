# AGENT.md - Market-Me Project Guidelines

## Build & Development Commands
- `npm run dev`: Run development server with turbopack
- `npm run build`: Build the project for production
- `npm run start`: Run the production build
- `npm run lint`: Run ESLint for code quality checks

## Code Style Guidelines
- **Imports**: Use absolute imports with `@/` prefix (e.g., `import { Button } from '@/components/ui/button'`)
- **Components**: Use TypeScript interfaces for props, functional components with React.FC
- **Naming**: PascalCase for components, camelCase for functions/variables
- **Types**: Use TypeScript strictly, interfaces for object shapes, explicit return types
- **Error Handling**: Use try/catch blocks for async operations
- **Styling**: Use Tailwind CSS with class-variance-authority for component variants
- **Framework**: Next.js with App Router (use client directive as needed)

## Component Structure
- UI components in components/ui/
- Page-specific components in respective directories
- Use shadcn/ui patterns with RadixUI primitives