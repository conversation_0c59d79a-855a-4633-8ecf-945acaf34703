/**
 * Server-side Image Conversion Utility
 * Uses Sharp for converting unsupported image formats to OpenAI-compatible formats
 */

import sharp from 'sharp';

// OpenAI supported formats
const OPENAI_SUPPORTED_FORMATS = ['png', 'jpeg', 'jpg', 'webp', 'gif'];

// Additional formats that Sharp can convert from
const CONVERTIBLE_FORMATS = [
  'avif', 'heic', 'heif', 'tiff', 'tif', 'bmp', 'svg', 'ico',
  'dng', 'cr2', 'nef', 'arw', 'rw2', 'orf', 'raf'
];

export interface ConversionResult {
  success: boolean;
  dataUrl?: string;
  originalFormat?: string;
  convertedFormat?: string;
  error?: string;
}

export class ImageConverter {
  /**
   * Convert unsupported image formats to OpenAI-compatible formats using Sharp
   */
  static async convertToOpenAIFormat(
    imageData: string,
    targetFormat: 'jpeg' | 'jpg' | 'png' | 'webp' = 'jpeg'
  ): Promise<ConversionResult> {
    try {
      console.log('[ImageConverter] Starting format conversion...');

      // Parse the data URL
      let base64Data: string;
      let detectedFormat: string | undefined;
      
      if (imageData.startsWith('data:')) {
        const matches = imageData.match(/^data:image\/([^;]+);base64,(.+)$/);
        if (!matches) {
          return {
            success: false,
            error: 'Invalid data URL format'
          };
        }
        detectedFormat = matches[1].toLowerCase();
        base64Data = matches[2];
      } else {
        // Assume it's raw base64
        base64Data = imageData;
      }

      console.log('[ImageConverter] Detected format:', detectedFormat);

      // Check if format is already supported
      if (detectedFormat && OPENAI_SUPPORTED_FORMATS.includes(detectedFormat)) {
        console.log('[ImageConverter] Format already supported, no conversion needed');
        return {
          success: true,
          dataUrl: imageData,
          originalFormat: detectedFormat,
          convertedFormat: detectedFormat
        };
      }

      // Convert base64 to buffer
      const imageBuffer = Buffer.from(base64Data, 'base64');
      
      // Use Sharp to detect format and convert
      const sharpImage = sharp(imageBuffer);
      const metadata = await sharpImage.metadata();
      
      const actualFormat = metadata.format?.toLowerCase();
      console.log('[ImageConverter] Sharp detected format:', actualFormat);

      if (!actualFormat) {
        return {
          success: false,
          error: 'Could not detect image format'
        };
      }

      // Check if we can convert this format
      if (!CONVERTIBLE_FORMATS.includes(actualFormat) && !OPENAI_SUPPORTED_FORMATS.includes(actualFormat)) {
        return {
          success: false,
          error: `Unsupported image format: ${actualFormat}. Supported formats: ${[...OPENAI_SUPPORTED_FORMATS, ...CONVERTIBLE_FORMATS].join(', ')}`
        };
      }

      // If already supported, return as-is
      if (OPENAI_SUPPORTED_FORMATS.includes(actualFormat)) {
        const mimeType = actualFormat === 'jpg' ? 'jpeg' : actualFormat;
        const dataUrl = `data:image/${mimeType};base64,${base64Data}`;
        return {
          success: true,
          dataUrl,
          originalFormat: actualFormat,
          convertedFormat: actualFormat
        };
      }

      // Convert to target format
      console.log('[ImageConverter] Converting from', actualFormat, 'to', targetFormat);
      
      let convertedBuffer: Buffer;
      const quality = 90; // High quality for marketing images
      
      switch (targetFormat.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
          convertedBuffer = await sharpImage
            .jpeg({ quality, mozjpeg: true })
            .toBuffer();
          break;
        case 'png':
          convertedBuffer = await sharpImage
            .png({ quality })
            .toBuffer();
          break;
        case 'webp':
          convertedBuffer = await sharpImage
            .webp({ quality })
            .toBuffer();
          break;
        default:
          // Default to JPEG for maximum compatibility
          convertedBuffer = await sharpImage
            .jpeg({ quality, mozjpeg: true })
            .toBuffer();
          break;
      }

      // Convert back to base64 data URL
      const convertedBase64 = convertedBuffer.toString('base64');
      const finalFormat = targetFormat === 'jpg' ? 'jpeg' : targetFormat;
      const convertedDataUrl = `data:image/${finalFormat};base64,${convertedBase64}`;

      console.log('[ImageConverter] Conversion successful:', {
        originalFormat: actualFormat,
        convertedFormat: finalFormat,
        originalSize: imageBuffer.length,
        convertedSize: convertedBuffer.length,
        compressionRatio: Math.round((1 - convertedBuffer.length / imageBuffer.length) * 100)
      });

      return {
        success: true,
        dataUrl: convertedDataUrl,
        originalFormat: actualFormat,
        convertedFormat: finalFormat
      };

    } catch (error) {
      console.error('[ImageConverter] Conversion failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown conversion error'
      };
    }
  }

  /**
   * Check if an image format is supported by OpenAI
   */
  static isOpenAISupported(format: string): boolean {
    return OPENAI_SUPPORTED_FORMATS.includes(format.toLowerCase());
  }

  /**
   * Get list of all supported formats (OpenAI + convertible)
   */
  static getSupportedFormats() {
    return {
      openAISupported: OPENAI_SUPPORTED_FORMATS,
      convertible: CONVERTIBLE_FORMATS,
      all: [...OPENAI_SUPPORTED_FORMATS, ...CONVERTIBLE_FORMATS]
    };
  }

  /**
   * Process an array of image data URLs, converting unsupported formats
   */
  static async processImageUrls(imageUrls: string[]): Promise<string[]> {
    const processedImages: string[] = [];
    
    for (const imageUrl of imageUrls) {
      try {
        // Check if format conversion is needed
        const formatMatch = imageUrl.match(/^data:image\/([^;]+)/);
        if (formatMatch) {
          const format = formatMatch[1].toLowerCase();
          if (!this.isOpenAISupported(format)) {
            console.log('[ImageConverter] Converting unsupported format:', format);
            const conversion = await this.convertToOpenAIFormat(imageUrl);
            if (conversion.success && conversion.dataUrl) {
              processedImages.push(conversion.dataUrl);
            } else {
              console.warn('[ImageConverter] Format conversion failed:', conversion.error);
            }
          } else {
            processedImages.push(imageUrl);
          }
        } else {
          console.warn('[ImageConverter] Could not detect format from data URL');
          processedImages.push(imageUrl); // Keep as-is if we can't detect format
        }
      } catch (error) {
        console.error('[ImageConverter] Error processing image URL:', error);
        // Continue with other images
      }
    }
    
    return processedImages;
  }
}
