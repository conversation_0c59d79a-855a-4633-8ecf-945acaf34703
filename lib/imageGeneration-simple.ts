import OpenAI, { toFile } from "openai";
import { checkCredits, deductCredits } from "../app/api/credits/utils";
import { getAdminDb } from "./firebaseAdmin";
import { uploadFileFromBase64, deleteFileByUrl } from "./blobStorageUtils";
import { Timestamp } from "firebase-admin/firestore";
import fs from "fs/promises";
import path from "path";
import os from "os";
import sharp from "sharp";
import { v4 as uuidv4 } from "uuid";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || ""
});

export interface SimplifiedImageParams {
  productImageBase64: string;
  imagePrompt: string;
  aspectRatio?: 'square' | 'portrait' | 'landscape';
  quality?: 'high' | 'medium' | 'low';
  productDescription?: string;
}

export interface SimplifiedImageResult {
  success: boolean;
  image?: {
    id: string;
    url: string;
    createdAt: string;
    prompt: string;
  };
  creditsUsed?: number;
  error?: string;
  details?: string;
  errorType?: string;
}

const generateUniqueFilename = () => {
  const timestamp = Date.now();
  return `${timestamp}-${uuidv4()}`;
};

/**
 * Simplified image generation - no prompt generation layer, just direct OpenAI image edit
 */
export async function generateSingleImage(
  userId: string,
  params: SimplifiedImageParams
): Promise<SimplifiedImageResult> {
  console.log('[simplifiedImageGeneration] Starting single image generation for user:', userId);

  let tempDir: string | null = null;
  let uploadedImageUrl: string | null = null;

  try {
    // Validate required parameters
    if (!params.productImageBase64) {
      return {
        success: false,
        error: "No product image provided",
        details: "Please upload a product image to generate marketing content.",
        errorType: "missing_product_image"
      };
    }

    if (!params.imagePrompt) {
      return {
        success: false,
        error: "No image prompt provided",
        details: "Please provide a creative prompt for image generation.",
        errorType: "missing_prompt"
      };
    }

    // Check if user has enough credits
    const hasCredits = await checkCredits(userId, 1);
    if (!hasCredits) {
      return {
        success: false,
        error: "Insufficient credits",
        details: "You need 1 credit to generate an image. Please purchase more credits to continue.",
        errorType: "insufficient_credits"
      };
    }

    // Create temp directory for processing
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), "ai-agent-single-image-"));
    console.log('[simplifiedImageGeneration] Created temp directory:', tempDir);

    // Process product image
    console.log('[simplifiedImageGeneration] Processing base64 product image');
    
    // Remove data URL prefix if present
    const base64Data = params.productImageBase64.replace(/^data:image\/\w+;base64,/, "");
    
    // Create temp file from base64
    const productImagePath = path.join(tempDir, `product_${generateUniqueFilename()}.png`);
    await fs.writeFile(productImagePath, Buffer.from(base64Data, "base64"));
    
    // Optimize the image
    const optimizedPath = path.join(tempDir, `product_optimized_${generateUniqueFilename()}.png`);
    await sharp(productImagePath)
      .resize({
        width: 1024,
        height: 1024,
        fit: "inside",
        withoutEnlargement: true
      })
      .toFormat("png")
      .toFile(optimizedPath);
    
    console.log('[simplifiedImageGeneration] Processed and optimized product image');

    // Prepare product image file for OpenAI
    const productBuffer = await fs.readFile(optimizedPath);
    const productFile = await toFile(productBuffer, "product.png", { type: "image/png" });
    
    // Map aspect ratio to size parameter
    let size: string;
    switch (params.aspectRatio || 'square') {
      case "portrait":
        size = "1024x1536";
        break;
      case "landscape":
        size = "1536x1024";
        break;
      default: // square
        size = "1024x1024";
        break;
    }
    
    console.log('[simplifiedImageGeneration] Using OpenAI image edit with size:', size);
    console.log('[simplifiedImageGeneration] Using prompt:', params.imagePrompt);
    
    // Call OpenAI image edit endpoint directly
    const imageResult = await openai.images.edit({
      model: "gpt-image-1",
      image: productFile,
      prompt: params.imagePrompt,
      size: size as any,
      quality: (params.quality || 'high') as any,
      output_format: "webp" as any,
      output_compression: 85 as any,
    } as any);

    const generatedImageB64 = imageResult.data?.[0]?.b64_json;
    if (!generatedImageB64) {
      console.error('[simplifiedImageGeneration] No image data returned from OpenAI');
      return {
        success: false,
        error: "No image data returned",
        details: "OpenAI image generation did not return any image data. Please try again.",
        errorType: "api_error"
      };
    }

    console.log('[simplifiedImageGeneration] Successfully generated image');

    // Upload to Vercel Blob
    try {
      console.log('[simplifiedImageGeneration] Uploading image to Vercel Blob');
      uploadedImageUrl = await uploadFileFromBase64(generatedImageB64, `generated/${userId}`);
      console.log('[simplifiedImageGeneration] Successfully uploaded image to:', uploadedImageUrl);
    } catch (uploadError) {
      console.error('[simplifiedImageGeneration] Error uploading to Vercel Blob:', uploadError);
      return {
        success: false,
        error: "Failed to upload image",
        details: "Generated image could not be uploaded to storage. Please try again.",
        errorType: "upload_error"
      };
    }

    // Store metadata in Firestore
    try {
      const adminDb = getAdminDb();
      const docRef = await adminDb.collection("images").add({
        user_id: userId,
        image_url: uploadedImageUrl,
        input_image_reference: `AI Agent generated single image`,
        prompt: params.imagePrompt,
        aspect_ratio: params.aspectRatio || 'square',
        parameters: {
          source: 'ai_agent_simplified',
          generationMethod: 'single_call',
          productDescription: params.productDescription || null,
          quality: params.quality || 'high'
        },
        created_at: Timestamp.now()
      });

      console.log('[simplifiedImageGeneration] Saved image metadata to Firestore with ID:', docRef.id);

      // Deduct credits
      console.log('[simplifiedImageGeneration] Deducting 1 credit');
      const deductionSuccessful = await deductCredits(userId, 1);
      if (!deductionSuccessful) {
        console.error('[simplifiedImageGeneration] Failed to deduct credits');
        
        // Rollback: delete generated image since we couldn't deduct credits
        try {
          await deleteFileByUrl(uploadedImageUrl);
          console.log('[simplifiedImageGeneration] Rolled back image due to credit deduction failure');
        } catch (rollbackError) {
          console.error('[simplifiedImageGeneration] Failed to rollback image:', rollbackError);
        }
        
        return {
          success: false,
          error: "Failed to process payment",
          details: "Could not deduct credits for image generation. Please try again.",
          errorType: "payment_error"
        };
      }

      console.log('[simplifiedImageGeneration] Image generation completed successfully');

      return {
        success: true,
        image: {
          id: docRef.id,
          url: uploadedImageUrl,
          createdAt: new Date().toISOString(),
          prompt: params.imagePrompt
        },
        creditsUsed: 1
      };

    } catch (firestoreError) {
      console.error('[simplifiedImageGeneration] Error saving to Firestore:', firestoreError);
      
      // Clean up the uploaded image since we couldn't save metadata
      if (uploadedImageUrl) {
        try {
          await deleteFileByUrl(uploadedImageUrl);
          console.log('[simplifiedImageGeneration] Cleaned up orphaned image:', uploadedImageUrl);
        } catch (cleanupError) {
          console.error('[simplifiedImageGeneration] Failed to cleanup orphaned image:', cleanupError);
        }
      }
      
      return {
        success: false,
        error: "Failed to save image metadata",
        details: "Image was generated but could not be saved to database. Please try again.",
        errorType: "database_error"
      };
    }

  } catch (error) {
    console.error('[simplifiedImageGeneration] Error in image generation:', error);
    
    // Cleanup any uploaded images on failure
    if (uploadedImageUrl) {
      try {
        await deleteFileByUrl(uploadedImageUrl);
        console.log('[simplifiedImageGeneration] Cleaned up failed image:', uploadedImageUrl);
      } catch (cleanupError) {
        console.error('[simplifiedImageGeneration] Failed to cleanup image:', cleanupError);
      }
    }
    
    // Provide detailed error information for better agent context
    let errorType = "general";
    let errorMessage = "Unknown error occurred";
    let errorDetails = "An unexpected error occurred during image generation. Please try again.";
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Categorize errors for better agent handling
      if (error.message.includes('moderation') || error.message.includes('safety') || error.message.includes('content policy')) {
        errorType = "moderation_blocked";
        errorDetails = "The content was flagged by OpenAI's safety system. Try adjusting the image prompt or product description.";
      } else if (error.message.includes('quota') || error.message.includes('rate limit')) {
        errorType = "rate_limit";
        errorDetails = "OpenAI's service is currently busy. Please wait a moment and try again.";
      } else if (error.message.includes('network') || error.message.includes('timeout')) {
        errorType = "network_error";
        errorDetails = "There was a connection issue with the image generation service. Please check your internet connection and try again.";
      } else if (error.message.includes('payment') || error.message.includes('billing')) {
        errorType = "payment_error";
        errorDetails = "There was an issue with the payment processing. Please check your account billing status.";
      } else {
        errorType = "technical_error";
        errorDetails = `Technical error: ${error.message}. Please try again, and if the problem persists, contact support.`;
      }
    }
    
    return {
      success: false,
      error: errorMessage,
      details: errorDetails,
      errorType: errorType
    };
  } finally {
    // Cleanup temp files
    if (tempDir) {
      try {
        const files = await fs.readdir(tempDir);
        for (const file of files) {
          await fs.unlink(path.join(tempDir, file));
        }
        await fs.rmdir(tempDir);
        console.log('[simplifiedImageGeneration] Cleaned up temporary files');
      } catch (cleanupErr) {
        console.error('[simplifiedImageGeneration] Error cleaning up temp files:', cleanupErr);
      }
    }
  }
}
