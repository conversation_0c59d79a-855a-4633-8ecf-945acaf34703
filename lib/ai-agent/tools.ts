import { AgentTool } from '@/app/(dashboard)/create/ai-chat/types/agent';

export const AI_AGENT_TOOLS: AgentTool[] = [
  {
    type: 'function',
    function: {
      name: 'generateSingleImage',
      description: 'Generate a single professional marketing image for a fashion product using a specific creative prompt. The agent should call this multiple times with different prompts to create varied images.',
      parameters: {
        type: 'object',
        properties: {
          imagePrompt: {
            type: 'string',
            description: 'Creative prompt for image generation. Should be detailed and specific, describing the model, pose, lighting, background, and composition. Start with "Edit the reference product image to create a composition where:"'
          },
          productDescription: {
            type: 'string',
            description: 'Brief description of the product being marketed'
          },
          imageMessageIndex: {
            type: ['number', 'null'],
            description: 'Optional: Specify which message contains the product image to use (0 = first message, -1 = last message). If null, system will use the most recent user image.',
            minimum: -10,
            maximum: 10
          },
          aspectRatio: {
            type: ['string', 'null'],
            enum: ['square', 'portrait', 'landscape', null],
            description: 'Image dimensions. Square for Instagram posts, portrait for stories, landscape for website banners. Null for default (square).'
          },
          quality: {
            type: ['string', 'null'],
            enum: ['high', 'medium', 'low', null],
            description: 'Image quality setting. Null for default (high).'
          }
        },
        required: ['imagePrompt', 'productDescription'],
        additionalProperties: false
      },
      strict: true
    }
  },
  {
    type: 'function',
    function: {
      name: 'saveMemory',
      description: 'Save important information about the user for future reference. Use this to remember user preferences, brand style, feedback, goals, and any other contextual information that will help provide better assistance in future conversations.',
      parameters: {
        type: 'object',
        properties: {
          content: {
            type: 'string',
            description: 'The memory content to save. Should be concise but descriptive (max 200 chars). Examples: "Prefers minimalist aesthetic with neutral colors", "Brand targets millennial women", "Dislikes overly posed models"'
          },
          category: {
            type: ['string', 'null'],
            enum: ['brand_style', 'preferences', 'feedback', 'goals', 'audience', 'general', null],
            description: 'Category of the memory for better organization. Null for general category.'
          },
          context: {
            type: ['string', 'null'],
            description: 'Additional context about when or why this memory was created. Optional but helpful for future reference.'
          }
        },
        required: ['content', 'category', 'context'],
        additionalProperties: false
      },
      strict: true
    }
  },
  {
    type: 'function',
    function: {
      name: 'searchMemories',
      description: 'Search through saved memories about the user to provide personalized and contextual assistance. Use this to recall user preferences, past feedback, brand guidelines, or any other relevant information from previous conversations.',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query to find relevant memories. Can be keywords, categories, or natural language descriptions.'
          },
          category: {
            type: ['string', 'null'],
            enum: ['brand_style', 'preferences', 'feedback', 'goals', 'audience', 'general', null],
            description: 'Optional category filter to narrow search results. Null to search all categories.'
          },
          limit: {
            type: ['number', 'null'],
            description: 'Maximum number of memories to return (default: 5, max: 20). Null for default value.',
            minimum: 1,
            maximum: 20
          }
        },
        required: ['query', 'category', 'limit'],
        additionalProperties: false
      },
      strict: true
    }
  },
  {
    type: 'function',
    function: {
      name: 'getUserContext',
      description: 'Get comprehensive information about the current user including credits, subscription status, and recent activity.',
      parameters: {
        type: 'object',
        properties: {},
        required: [],
        additionalProperties: false
      },
      strict: true
    }
  },
  {
    type: 'function',
    function: {
      name: 'getRecentImages',
      description: 'Fetch the user\'s recently generated images to provide context and suggestions for new content.',
      parameters: {
        type: 'object',
        properties: {
          limit: {
            type: ['number', 'null'],
            description: 'Number of recent images to fetch (default: 5, max: 20). Pass null for default value.',
            minimum: 1,
            maximum: 20
          }
        },
        required: ['limit'],
        additionalProperties: false
      },
      strict: true
    }
  },
  {
    type: 'function',
    function: {
      name: 'analyzeGeneratedImages',
      description: 'Analyze generated marketing images to provide accurate descriptions of their content, styling, and visual elements. Use this after generating images to describe what was actually created.',
      parameters: {
        type: 'object',
        properties: {
          imageUrls: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: 'Array of image URLs to analyze for accurate description',
            maxItems: 8
          },
          analysisType: {
            type: ['string', 'null'],
            enum: ['detailed', 'brief', 'marketing', null],
            description: 'Type of analysis to perform. detailed = comprehensive analysis, brief = short description, marketing = focus on marketing elements. Default is marketing.'
          },
          focusAreas: {
            type: ['array', 'null'],
            items: {
              type: 'string',
              enum: ['model', 'product', 'background', 'lighting', 'composition', 'style', 'mood']
            },
            description: 'Optional: specific areas to focus the analysis on'
          }
        },
        required: ['imageUrls'],
        additionalProperties: false
      },
      strict: true
    }
  }
];

// Helper function to extract product image from conversation history
// Uses OpenAI format only
export function extractProductImageFromHistory(
  conversationHistory: any[],
  imageMessageIndex?: number | null
): string | null {
  console.log('[tools] Extracting product image from history:', {
    historyLength: conversationHistory.length,
    imageMessageIndex
  });

  // Helper function to extract image from a message (OpenAI format only)
  const extractImageFromMessage = (msg: any, messageIndex: number): string | null => {
    if (msg.role !== 'user') {
      return null;
    }

    // Check OpenAI format (content array with image_url objects)
    if (Array.isArray(msg.content)) {
      for (const contentItem of msg.content) {
        if (contentItem.type === 'image_url' && contentItem.image_url?.url) {
          console.log(`[tools] Found product image in message ${messageIndex} (OpenAI format):`, {
            imagePreview: contentItem.image_url.url.substring(0, 50) + '...'
          });
          return contentItem.image_url.url;
        }
      }
    }

    return null;
  };

  // If no specific index provided, find the most recent user message with images
  if (imageMessageIndex === null || imageMessageIndex === undefined) {
    // Search from most recent to oldest
    for (let i = conversationHistory.length - 1; i >= 0; i--) {
      const msg = conversationHistory[i];
      const imageUrl = extractImageFromMessage(msg, i);
      if (imageUrl) {
        console.log('[tools] Found product image in message at index:', i);
        return imageUrl;
      }
    }
  } else {
    // Use specific index (-1 means last message, -2 means second to last, etc.)
    const actualIndex = imageMessageIndex < 0
      ? conversationHistory.length + imageMessageIndex
      : imageMessageIndex;
    
    if (actualIndex >= 0 && actualIndex < conversationHistory.length) {
      const msg = conversationHistory[actualIndex];
      const imageUrl = extractImageFromMessage(msg, actualIndex);
      if (imageUrl) {
        console.log('[tools] Found product image at specified index:', actualIndex);
        return imageUrl;
      }
    }
  }

  // Debug: Log structure of all messages to help understand format
  console.log('[tools] No product image found. Message structure debug:');
  conversationHistory.forEach((msg, index) => {
    if (msg.role === 'user') {
      console.log(`[tools] Message ${index}:`, {
        role: msg.role,
        hasContent: !!msg.content,
        contentType: Array.isArray(msg.content) ? 'array' : typeof msg.content,
        contentLength: Array.isArray(msg.content) ? msg.content.length : 0,
        contentTypes: Array.isArray(msg.content) ? msg.content.map((item: any) => item.type) : 'N/A'
      });
    }
  });

  console.log('[tools] No product image found in conversation history');
  return null;
}

// Helper function to validate single image generation parameters
export function validateSingleImageParams(userCredits: number): { valid: boolean; error?: string } {
  console.log('[tools] Validating generateSingleImage params:', { userCredits });
  
  if (userCredits < 1) {
    console.log('[tools] Validation failed: Insufficient credits', { available: userCredits });
    return { 
      valid: false, 
      error: `Cannot generate image. You need at least 1 credit. You have ${userCredits} credits available.` 
    };
  }

  console.log('[tools] Validation passed');
  return { valid: true };
}

// Smart parameter suggestions based on product type and conversation
export function suggestParameters(productType?: string, brandStyle?: string) {
  const suggestions: any = {};

  // Product type-based suggestions
  if (productType) {
    const type = productType.toLowerCase();
    
    if (type.includes('dress') || type.includes('gown')) {
      suggestions.vibe = 'elegant';
      suggestions.photoStyle = 'editorial';
      suggestions.background = 'studio';
    } else if (type.includes('casual') || type.includes('tshirt') || type.includes('jeans')) {
      suggestions.vibe = 'casual';
      suggestions.photoStyle = 'lifestyle';
      suggestions.background = 'outdoor';
    } else if (type.includes('accessories') || type.includes('jewelry')) {
      suggestions.showPerson = true;
      suggestions.photoStyle = 'portrait';
      suggestions.background = 'minimal';
    }
  }

  // Brand style-based suggestions
  if (brandStyle) {
    const style = brandStyle.toLowerCase();
    
    if (style.includes('luxury') || style.includes('premium')) {
      suggestions.vibe = 'elegant';
      suggestions.photoStyle = 'editorial';
      suggestions.background = 'studio';
    } else if (style.includes('streetwear') || style.includes('urban')) {
      suggestions.vibe = 'edgy';
      suggestions.photoStyle = 'lifestyle';
      suggestions.background = 'urban';
    }
  }

  return suggestions;
}
