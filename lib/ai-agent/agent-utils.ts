/**
 * Shared Agent Utilities
 *
 * This file contains reusable utility functions for the AI agent,
 * focusing on non-image related operations.
 * Image-related operations have been moved to ImageService.
 */

/**
 * Extract relevant description from web search response text based on URL context
 */
export function extractDescriptionFromResponse(responseText: string, url: string): string | null {
  try {
    // Find the context around the URL mention
    const urlIndex = responseText.indexOf(url);
    if (urlIndex === -1) return null;
    
    // Get surrounding text (100 chars before and after)
    const start = Math.max(0, urlIndex - 100);
    const end = Math.min(responseText.length, urlIndex + url.length + 100);
    const context = responseText.substring(start, end);
    
    // Extract sentences that mention fashion, style, or relevant terms
    const sentences = context.split(/[.!?]+/);
    for (const sentence of sentences) {
      const lowerSentence = sentence.toLowerCase();
      if (lowerSentence.includes('fashion') || 
          lowerSentence.includes('style') || 
          lowerSentence.includes('campaign') ||
          lowerSentence.includes('brand') ||
          lowerSentence.includes('trend')) {
        return sentence.trim();
      }
    }
    
    return null;
  } catch (error) {
    console.error('[AgentUtils] Error extracting description:', error);
    return null;
  }
}


/**
 * Create user-friendly error messages for common agent errors
 */
export function createUserFriendlyError(error: any, context: 'image_generation' | 'web_search' | 'memory' | 'general'): string {
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  switch (context) {
    case 'image_generation':
      if (errorMessage.includes('unsupported image') || errorMessage.includes('image format')) {
        return 'The uploaded image format is not supported by OpenAI. Please ensure your image is in PNG, JPEG, WEBP, or GIF format and try again.';
      }
      if (errorMessage.includes('credits')) {
        return 'You don\'t have enough credits to generate images. Please upgrade your plan or purchase more credits.';
      }
      return `I encountered an error while generating your images: ${errorMessage}. No credits were charged. Please try again.`;
      
    case 'web_search':
      return `I had trouble finding inspiration for that search. ${errorMessage} Would you like me to try a different search term or suggest some current fashion trends instead?`;
      
    case 'memory':
      return `I had trouble saving that information to memory: ${errorMessage}. Your request was still processed successfully.`;
      
    default:
      return `I encountered an error while processing your request: ${errorMessage}. Please try again.`;
  }
}