/**
 * OpenAI Agent - Simplified Function Calling Implementation
 * 
 * Purpose: Implements OpenAI's recommended function calling patterns with simplified image generation
 * Performance Notes: Uses proper error handling and result communication
 * Dependencies: OpenAI Chat Completions API, simplified image generation
 */

import OpenAI from 'openai';
import { UserContext } from '@/app/(dashboard)/create/ai-chat/types/conversation';
import { generateSingleImage, SimplifiedImageParams } from '../imageGeneration-simple';
import { memoryService } from './memory-service';
import { validateSingleImageParams } from './tools';
import { YAYA_SYSTEM_PROMPT } from './prompts';
import { ImageService } from '../services/image-service';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || ""
});

export interface AgentResponse {
  message: string;
  toolResults?: ToolResult[];
  generatedImages?: any[];
  structuredOutput?: {
    type: 'image_generation';
    data: any;
  };
}

export interface ToolResult {
  type: 'memory' | 'image_generation';
  data: any;
  success: boolean;
  context: string;
}

export interface ConversationMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  images?: string[];
  toolResults?: ToolResult[];
}

export interface StreamingCallbacks {
  onTextDelta: (delta: string, accumulated: string) => void;
  onToolCallStart: (toolName: string, toolId: string) => void;
  onToolCallProgress: (toolName: string, toolId: string, progress: any) => void;
  onToolCallComplete: (toolName: string, toolId: string, result: any) => void;
  onImagesReady?: (images: any[], toolResults: ToolResult[]) => void; // New callback for immediate image display
  onComplete: (finalResponse: AgentResponse) => void;
  onError: (error: Error) => void;
}

/**
 * OpenAI Agent following function calling best practices
 */
export class OpenAIAgent {
  private userContext?: UserContext;

  constructor(userContext?: UserContext) {
    this.userContext = userContext;
  }

  /**
   * Generate welcome message
   */
  async generateWelcomeMessage(): Promise<AgentResponse> {
    const welcomeMessage = `Hi! I'm Yaya, your AI marketing assistant! 🎨

I help fashion brands create stunning product marketing images. Simply upload a photo of your product, and I'll generate professional lifestyle and editorial shots that showcase it beautifully.

What would you like to create today?`;

    return { message: welcomeMessage };
  }

  /**
   * Process message with proper function calling
   */
  async processMessage(
    userMessage: string,
    images?: string[],
    conversationHistory: ConversationMessage[] = []
  ): Promise<AgentResponse> {
    try {
      console.log('[OpenAIAgent] Processing message with simplified function calling');

      // Convert blob URLs in conversation history
      const safeConversationHistory = await ImageService.convertBlobUrlsInHistory(conversationHistory);

      // Build messages for OpenAI
      const messages: any[] = [
        { role: 'system', content: this.buildSystemPrompt() }
      ];

      // Add conversation history
      this.addConversationHistory(messages, safeConversationHistory);

      // Add current user message
      this.addUserMessage(messages, userMessage, images);

      // Add user context
      this.addUserContext(messages);

      // Define tools with proper schemas
      const tools = this.defineTools();

      // Make initial API call
      const completion = await openai.chat.completions.create({
        model: 'gpt-4.1',
        messages,
        tools: tools as any,
        tool_choice: 'auto',
        temperature: 0.7,
        max_tokens: 1500,
        // Enable parallel tool calls for multiple image generation
        parallel_tool_calls: true // Allow multiple simultaneous tool calls
      });

      const choice = completion.choices[0];
      const message = choice.message;

      // Handle function calls properly (supports multiple parallel calls)
      if (message.tool_calls && message.tool_calls.length > 0) {
        return await this.handleParallelFunctionCalls(message.tool_calls, messages, safeConversationHistory);
      }

      // Regular text response
      return {
        message: message.content || "I'm here to help! What would you like to create today?"
      };

    } catch (error) {
      console.error('[OpenAIAgent] Error processing message:', error);
      return {
        message: "I encountered an error while processing your request. Please try again."
      };
    }
  }

  /**
   * Handle multiple parallel function calls
   */
  private async handleParallelFunctionCalls(
    toolCalls: any[],
    messages: any[],
    conversationHistory: ConversationMessage[]
  ): Promise<AgentResponse> {
    console.log(`[OpenAIAgent] Executing ${toolCalls.length} parallel function calls`);

    try {
      // Execute all function calls in parallel
      const functionPromises = toolCalls.map(async (toolCall) => {
        const functionName = toolCall.function.name;
        const functionArgs = JSON.parse(toolCall.function.arguments);
        
        console.log(`[OpenAIAgent] Parallel execution: ${functionName}`, functionArgs);
        
        try {
          const rawResult = await this.executeFunctionCall(functionName, functionArgs, conversationHistory);
          return {
            toolCall,
            rawResult,
            success: true
          };
        } catch (error) {
          console.error(`[OpenAIAgent] Parallel function error (${functionName}):`, error);
          return {
            toolCall,
            rawResult: {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              errorType: 'execution_error',
              details: `Failed to execute ${functionName}`
            },
            success: false
          };
        }
      });

      // Wait for all parallel calls to complete
      const results = await Promise.all(functionPromises);
      
      console.log(`[OpenAIAgent] All ${results.length} parallel calls completed`);

      // Add assistant's tool calls to conversation
      messages.push({
        role: 'assistant',
        content: null,
        tool_calls: toolCalls.map(toolCall => ({
          id: toolCall.id,
          type: 'function',
          function: {
            name: toolCall.function.name,
            arguments: toolCall.function.arguments
          }
        }))
      });

      // Add all tool results to conversation
      results.forEach(({ toolCall, rawResult }) => {
        messages.push({
          role: 'tool',
          content: JSON.stringify(rawResult),
          tool_call_id: toolCall.id
        });
      });

      // Get model's response to all tool results
      const followUpCompletion = await openai.chat.completions.create({
        model: 'gpt-4.1',
        messages,
        temperature: 0.7,
        max_tokens: 1500
      });

      const modelResponse = followUpCompletion.choices[0].message;
      const responseMessage = modelResponse.content || "I've completed your requests.";

      // Process results and build response
      const toolResults: ToolResult[] = [];
      const generatedImages: any[] = [];
      let structuredOutput: any = null;

      results.forEach(({ toolCall, rawResult }) => {
        const functionName = toolCall.function.name;
        
        // Create tool result for frontend
        const toolResult: ToolResult = {
          type: functionName === 'generateSingleImage' ? 'image_generation' : 'memory',
          data: rawResult,
          success: rawResult.success,
          context: functionName
        };
        toolResults.push(toolResult);

        // Collect successful images
        if (functionName === 'generateSingleImage' && rawResult.success && rawResult.image) {
          generatedImages.push(rawResult.image);
        }
      });

      // Set structured output if images were generated
      if (generatedImages.length > 0) {
        structuredOutput = {
          type: 'image_generation' as const,
          data: {
            success: true,
            totalImages: generatedImages.length,
            images: generatedImages,
            parallelExecution: true
          }
        };
      }

      return {
        message: responseMessage,
        toolResults,
        generatedImages: generatedImages.length > 0 ? generatedImages : undefined,
        structuredOutput
      };

    } catch (error) {
      console.error(`[OpenAIAgent] Parallel function execution error:`, error);
      
      return {
        message: "I encountered an error while processing your requests. Please try again.",
        toolResults: [{
          type: 'image_generation',
          data: {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            errorType: 'parallel_execution_error'
          },
          success: false,
          context: 'parallel execution failed'
        }]
      };
    }
  }

  /**
   * Handle function call with proper OpenAI flow
   */
  private async handleFunctionCall(
    toolCall: any,
    messages: any[],
    conversationHistory: ConversationMessage[]
  ): Promise<AgentResponse> {
    const functionName = toolCall.function.name;
    const functionArgs = JSON.parse(toolCall.function.arguments);

    console.log(`[OpenAIAgent] Executing function: ${functionName}`, functionArgs);

    try {
      // Execute function and get RAW result (no interpretation)
      const rawResult = await this.executeFunctionCall(functionName, functionArgs, conversationHistory);
      
      console.log(`[OpenAIAgent] Raw function result:`, {
        functionName,
        success: rawResult.success,
        hasError: !!rawResult.error,
        details: rawResult
      });

      // Add assistant's tool call to conversation
      messages.push({
        role: 'assistant',
        content: null,
        tool_calls: [{
          id: toolCall.id,
          type: 'function',
          function: {
            name: functionName,
            arguments: JSON.stringify(functionArgs)
          }
        }]
      });

      // Add the RAW tool result - let the model interpret it
      messages.push({
        role: 'tool',
        content: JSON.stringify(rawResult), // Give model the ACTUAL result
        tool_call_id: toolCall.id
      });

      // Let the model respond to the tool result
      const followUpCompletion = await openai.chat.completions.create({
        model: 'gpt-4.1',
        messages,
        temperature: 0.7,
        max_tokens: 1500
      });

      const modelResponse = followUpCompletion.choices[0].message;
      const responseMessage = modelResponse.content || "I've processed your request.";

      // Create proper tool result for frontend
      const toolResult: ToolResult = {
        type: functionName === 'generateSingleImage' ? 'image_generation' : 'memory',
        data: rawResult,
        success: rawResult.success,
        context: functionName
      };

      // Return response based on function type and result
      if (functionName === 'generateSingleImage' && rawResult.success && rawResult.image) {
        return {
          message: responseMessage,
          generatedImages: [rawResult.image],
          toolResults: [toolResult],
          structuredOutput: {
            type: 'image_generation',
            data: rawResult
          }
        };
      }

      return {
        message: responseMessage,
        toolResults: [toolResult]
      };

    } catch (error) {
      console.error(`[OpenAIAgent] Function execution error:`, error);
      
      // Create error result
      const errorResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorType: 'execution_error',
        details: `Failed to execute ${functionName}`
      };

      // Add error to conversation
      messages.push({
        role: 'assistant',
        content: null,
        tool_calls: [{
          id: toolCall.id,
          type: 'function',
          function: {
            name: functionName,
            arguments: JSON.stringify(functionArgs)
          }
        }]
      });

      messages.push({
        role: 'tool',
        content: JSON.stringify(errorResult),
        tool_call_id: toolCall.id
      });

      // Get model's error response
      const errorResponse = await openai.chat.completions.create({
        model: 'gpt-4.1',
        messages,
        temperature: 0.7,
        max_tokens: 1500
      });

      return {
        message: errorResponse.choices[0].message.content || "I encountered an error processing your request.",
        toolResults: [{
          type: functionName === 'generateSingleImage' ? 'image_generation' : 'memory',
          data: errorResult,
          success: false,
          context: `${functionName} execution failed`
        }]
      };
    }
  }

  /**
   * Execute function call and return HONEST, RAW results
   */
  private async executeFunctionCall(
    functionName: string, 
    args: any, 
    conversationHistory?: ConversationMessage[]
  ): Promise<any> {
    switch (functionName) {
      case 'generateSingleImage':
        return await this.handleGenerateSingleImage(args, conversationHistory);
      
      case 'saveMemory':
        return await this.handleSaveMemory(args);
      
      case 'analyzeGeneratedImages':
        return await this.handleAnalyzeGeneratedImages(args);
      
      default:
        throw new Error(`Unknown function: ${functionName}`);
    }
  }

  /**
   * Handle single image generation with HONEST result reporting
   */
  private async handleGenerateSingleImage(args: any, conversationHistory?: ConversationMessage[]): Promise<any> {
    try {
      // Extract product image
      const { extractProductImageFromHistory } = await import('./tools');
      const productImageData = extractProductImageFromHistory(
        conversationHistory || [],
        args.imageMessageIndex
      );
      
      if (!productImageData) {
        return {
          success: false,
          error: "No product image found in conversation",
          errorType: "missing_product_image",
          userMessage: "Please upload a product image first so I can create marketing content for it!"
        };
      }

      // Validate parameters
      const validation = validateSingleImageParams(this.userContext?.credits || 0);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
          errorType: "validation_error",
          userMessage: validation.error
        };
      }

      // Prepare parameters for single image generation
      const imageGenParams: SimplifiedImageParams = {
        productImageBase64: productImageData,
        imagePrompt: args.imagePrompt,
        aspectRatio: args.aspectRatio || 'square',
        quality: args.quality || 'high',
        productDescription: args.productDescription
      };

      // Generate single image
      const result = await generateSingleImage(this.userContext?.id || '', imageGenParams);
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || "Failed to generate image",
          errorType: result.errorType || "generation_failed",
          creditsUsed: result.creditsUsed || 0,
          userMessage: "I wasn't able to generate the image. Let me try a different approach."
        };
      }

      // Success
      return {
        success: true,
        image: result.image,
        creditsUsed: result.creditsUsed || 0,
        userMessage: "Perfect! I've generated your marketing image."
      };

    } catch (error) {
      console.error('[OpenAIAgent] Error in handleGenerateSingleImage:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorType: 'execution_error',
        userMessage: "I encountered a technical error while generating the image. Please try again."
      };
    }
  }

  /**
   * Handle saving memories
   */
  private async handleSaveMemory(args: any): Promise<any> {
    try {
      if (!this.userContext?.id) {
        return {
          success: false,
          error: 'User context not available',
          errorType: 'missing_context'
        };
      }

      const result = await memoryService.saveMemory(
        this.userContext.id,
        args.content,
        args.category || 'general',
        args.context
      );

      return result;

    } catch (error) {
      console.error('[OpenAIAgent] Error in handleSaveMemory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save memory',
        errorType: 'execution_error'
      };
    }
  }

  /**
   * Handle analyzing generated images using OpenAI Vision
   * Returns RAW analysis data for the LLM to interpret
   */
  private async handleAnalyzeGeneratedImages(args: any): Promise<any> {
    try {
      const { imageUrls, analysisType = 'marketing', focusAreas } = args;
      
      if (!imageUrls || !Array.isArray(imageUrls) || imageUrls.length === 0) {
        return {
          success: false,
          error: 'No image URLs provided for analysis',
          errorType: 'missing_images'
        };
      }

      console.log('[OpenAIAgent] Analyzing', imageUrls.length, 'generated images with Vision API');

      // Build system prompt for accurate image analysis
      const systemPrompt = `You are a fashion marketing expert analyzing generated marketing images. Describe exactly what you see in the images - be factual and specific about:
- The actual product and how it appears
- Model appearance, pose, and styling (if present)
- Background, setting, and lighting
- Overall composition and mood
- Quality and marketing effectiveness

Be honest about what you observe - don't assume or infer beyond what's visible.`;

      let userPrompt = 'Analyze these marketing images and describe exactly what was generated:';
      
      if (focusAreas && focusAreas.length > 0) {
        userPrompt += ` Pay special attention to: ${focusAreas.join(', ')}.`;
      }

      // Prepare content with all images for Vision API
      const content: any[] = [{ type: 'text', text: userPrompt }];

      // Add each image URL directly (Vercel URLs are public)
      for (const imageUrl of imageUrls) {
        content.push({
          type: 'image_url',
          image_url: {
            url: imageUrl,
            detail: 'high'
          }
        });
      }

      // Call OpenAI Vision API
      const response = await openai.chat.completions.create({
        model: 'gpt-4.1',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content }
        ],
        max_tokens: 1500,
        temperature: 0.3
      });

      const analysis = response.choices[0].message.content;
      
      if (!analysis) {
        return {
          success: false,
          error: 'No analysis returned from OpenAI Vision API',
          errorType: 'api_error'
        };
      }

      console.log('[OpenAIAgent] Successfully analyzed', imageUrls.length, 'images');

      // Return RAW analysis data for the LLM to interpret
      return {
        success: true,
        imageUrls,
        imageCount: imageUrls.length,
        analysis: analysis.trim(),
        analysisType,
        focusAreas: focusAreas || [],
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('[OpenAIAgent] Error in handleAnalyzeGeneratedImages:', error);
      
      let errorType = 'execution_error';
      let errorMessage = 'Failed to analyze images';
      
      if (error instanceof Error) {
        errorMessage = error.message;
        
        if (error.message.includes('rate limit') || error.message.includes('quota')) {
          errorType = 'rate_limit';
        } else if (error.message.includes('network') || error.message.includes('timeout')) {
          errorType = 'network_error';
        }
      }
      
      return {
        success: false,
        error: errorMessage,
        errorType,
        imageUrls: args.imageUrls || [],
        imageCount: args.imageUrls?.length || 0
      };
    }
  }

  /**
   * Build system prompt following OpenAI best practices
   */
  private buildSystemPrompt(): string {
    return `${YAYA_SYSTEM_PROMPT}

# Function Calling Guidelines

## Critical Rules:
- Use generateSingleImage to create one image at a time with specific creative prompts
- To create multiple images, call generateSingleImage multiple times with different creative prompts for variety
- Each call to generateSingleImage should have a unique, detailed prompt describing a different composition
- Always start image prompts with "Edit the reference product image to create a composition where:"

## Image Generation Strategy:
- For multiple images, create diverse prompts that vary: poses, backgrounds, lighting, angles, moods
- Example approach for 4 images:
  1. "Edit the reference product image to create a composition where: an elegant model wears the product on a modern rooftop at golden hour..."
  2. "Edit the reference product image to create a composition where: a model showcases the product in a minimalist studio with dramatic lighting..."
  3. "Edit the reference product image to create a composition where: the product is styled in an urban street setting with natural sunlight..."
  4. "Edit the reference product image to create a composition where: a model presents the product in a luxurious indoor environment..."

## Function Behavior:
- Each generateSingleImage call is independent and completes immediately
- No "processing" or "still rendering" - each call either succeeds or fails
- Be creative with prompts to ensure visual variety between images
- Use your creative expertise to craft compelling, diverse marketing scenarios

## Error Handling:
- If an image generation fails, try a different creative approach
- Communicate honestly about any limitations or failures
- Provide helpful next steps when things don't go perfectly`;
  }

  /**
   * Define tools with proper schemas
   */
  private defineTools() {
    return [
      {
        type: 'function',
        function: {
          name: 'generateSingleImage',
          description: 'Generate a single professional marketing image using a creative prompt. Agent should call this multiple times with different prompts for variety.',
          parameters: {
            type: 'object',
            properties: {
              imagePrompt: {
                type: 'string',
                description: 'Creative detailed prompt for image generation. Should start with "Edit the reference product image to create a composition where:" and include specific details about model, pose, lighting, background, and composition.'
              },
              productDescription: {
                type: 'string',
                description: 'Brief description of the product being marketed'
              },
              imageMessageIndex: {
                type: ['number', 'null'],
                description: 'Optional: specify which message contains the product image'
              },
              aspectRatio: { 
                type: 'string', 
                enum: ['square', 'portrait', 'landscape'],
                description: 'Image dimensions'
              },
              quality: { 
                type: 'string', 
                enum: ['high', 'medium', 'low'],
                description: 'Image quality setting'
              }
            },
            required: ['imagePrompt', 'productDescription'],
            additionalProperties: false
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'saveMemory',
          description: 'Save important user information for future reference',
          parameters: {
            type: 'object',
            properties: {
              content: { type: 'string' },
              category: { type: 'string', enum: ['brand_style', 'preferences', 'feedback', 'goals', 'audience', 'general'] },
              context: { type: 'string' }
            },
            required: ['content'],
            additionalProperties: false
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'analyzeGeneratedImages',
          description: 'Analyze generated marketing images to provide accurate descriptions of their content, styling, and visual elements. Use this after generating images to describe what was actually created.',
          parameters: {
            type: 'object',
            properties: {
              imageUrls: {
                type: 'array',
                items: { type: 'string' },
                description: 'Array of image URLs to analyze for accurate description',
                maxItems: 8
              },
              analysisType: {
                type: 'string',
                enum: ['detailed', 'brief', 'marketing'],
                description: 'Type of analysis to perform. Default is marketing.'
              },
              focusAreas: {
                type: 'array',
                items: {
                  type: 'string',
                  enum: ['model', 'product', 'background', 'lighting', 'composition', 'style', 'mood']
                },
                description: 'Optional: specific areas to focus the analysis on'
              }
            },
            required: ['imageUrls'],
            additionalProperties: false
          }
        }
      }
    ];
  }

  // Helper methods for building messages
  private addConversationHistory(messages: any[], history: ConversationMessage[]) {
    for (const msg of history) {
      if (msg.images && msg.images.length > 0) {
        const content: any[] = [{ type: 'text', text: msg.content }];
        msg.images.forEach(imageUrl => {
          if (imageUrl.startsWith('data:image/')) {
            content.push({
              type: 'image_url',
              image_url: { url: imageUrl, detail: 'high' }
            });
          }
        });
        messages.push({ role: msg.role, content });
      } else {
        messages.push({ role: msg.role, content: msg.content });
      }
    }
  }

  private addUserMessage(messages: any[], userMessage: string, images?: string[]) {
    const userContent: any[] = [{ type: 'text', text: userMessage }];
    
    if (images && images.length > 0) {
      images.forEach(imageUrl => {
        if (imageUrl.startsWith('data:image/')) {
          userContent.push({
            type: 'image_url',
            image_url: { url: imageUrl, detail: 'high' }
          });
        }
      });
    }

    messages.push({
      role: 'user',
      content: userContent.length === 1 ? userMessage : userContent
    });
  }

  private addUserContext(messages: any[]) {
    if (this.userContext) {
      const contextMessage = `User context: ${this.userContext.name ? `Name: ${this.userContext.name}, ` : ''}Credits: ${this.userContext.credits}, Email: ${this.userContext.email}`;
      messages.push({
        role: 'system',
        content: contextMessage
      });
    }
  }

  /**
   * Process message with streaming support
   */
  async processMessageStreaming(
    userMessage: string,
    images: string[] | undefined,
    conversationHistory: ConversationMessage[],
    callbacks: StreamingCallbacks
  ): Promise<void> {
    try {
      console.log('[OpenAIAgent] Processing message with streaming');

      // Convert blob URLs in conversation history
      const safeConversationHistory = await ImageService.convertBlobUrlsInHistory(conversationHistory);

      // Build messages for OpenAI
      const messages: any[] = [
        { role: 'system', content: this.buildSystemPrompt() }
      ];

      // Add conversation history
      this.addConversationHistory(messages, safeConversationHistory);

      // Add current user message
      this.addUserMessage(messages, userMessage, images);

      // Add user context
      this.addUserContext(messages);

      // Define tools with proper schemas
      const tools = this.defineTools();

      // Make streaming API call
      const stream = await openai.chat.completions.create({
        model: 'gpt-4.1',
        messages,
        tools: tools as any,
        tool_choice: 'auto',
        temperature: 0.7,
        max_tokens: 1500,
        parallel_tool_calls: true,
        stream: true
      });

      let accumulatedText = '';
      const currentToolCalls: any = {};
      const finalResponse: AgentResponse = { message: '' };
      let hasToolCalls = false;

      // Process streaming events
      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        
        if (!choice) continue;

        // Handle text content streaming
        if (choice.delta.content) {
          accumulatedText += choice.delta.content;
          callbacks.onTextDelta(choice.delta.content, accumulatedText);
        }

        // Handle tool calls
        if (choice.delta.tool_calls) {
          hasToolCalls = true;
          
          for (const toolCallDelta of choice.delta.tool_calls) {
            const index = toolCallDelta.index;
            
            if (!currentToolCalls[index]) {
              currentToolCalls[index] = {
                id: toolCallDelta.id,
                type: 'function',
                function: {
                  name: toolCallDelta.function?.name || '',
                  arguments: ''
                }
              };
              
              // Notify tool call start
              if (toolCallDelta.function?.name) {
                callbacks.onToolCallStart(
                  toolCallDelta.function.name,
                  toolCallDelta.id || `tool_${index}`
                );
              }
            }

            // Update function name if provided
            if (toolCallDelta.function?.name) {
              currentToolCalls[index].function.name = toolCallDelta.function.name;
            }

            // Accumulate arguments
            if (toolCallDelta.function?.arguments) {
              currentToolCalls[index].function.arguments += toolCallDelta.function.arguments;
              
              // Notify progress with current arguments
              callbacks.onToolCallProgress(
                currentToolCalls[index].function.name,
                currentToolCalls[index].id,
                {
                  arguments: currentToolCalls[index].function.arguments,
                  isComplete: false
                }
              );
            }
          }
        }

        // Check if streaming is complete
        if (choice.finish_reason === 'stop' || choice.finish_reason === 'tool_calls') {
          break;
        }
      }

      // Handle tool calls if any were made (PARALLEL EXECUTION)
      if (hasToolCalls && Object.keys(currentToolCalls).length > 0) {
        console.log(`[Streaming] Executing ${Object.keys(currentToolCalls).length} tool calls in parallel`);
        
        // Execute all tool calls in parallel
        const toolCallPromises = Object.values(currentToolCalls).map(async (toolCall: any) => {
          try {
            // Execute the function
            const functionName = toolCall.function.name;
            const functionArgs = JSON.parse(toolCall.function.arguments);
            
            console.log(`[Streaming] Parallel execution: ${functionName}`, functionArgs);
            
            const rawResult = await this.executeFunctionCall(
              functionName,
              functionArgs,
              safeConversationHistory
            );
            
            // Notify completion
            callbacks.onToolCallComplete(
              functionName,
              toolCall.id,
              rawResult
            );
            
            // Store result
            const toolResult: ToolResult = {
              type: functionName === 'generateSingleImage' ? 'image_generation' : 'memory',
              data: rawResult,
              success: rawResult.success,
              context: functionName
            };
            
            return { toolCall, toolResult, rawResult };
            
          } catch (error) {
            console.error(`[Streaming] Tool execution error:`, error);
            
            const errorResult = {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              errorType: 'execution_error'
            };
            
            callbacks.onToolCallComplete(
              toolCall.function.name,
              toolCall.id,
              errorResult
            );
            
            const toolResult: ToolResult = {
              type: toolCall.function.name === 'generateSingleImage' ? 'image_generation' : 'memory',
              data: errorResult,
              success: false,
              context: `${toolCall.function.name} execution failed`
            };
            
            return { toolCall, toolResult, rawResult: errorResult };
          }
        });
        
        // Wait for all parallel calls to complete
        const parallelResults = await Promise.all(toolCallPromises);
        
        console.log(`[Streaming] All ${parallelResults.length} parallel tool calls completed`);
        
        // Process results
        const toolResults: ToolResult[] = [];
        const generatedImages: any[] = [];
        
        parallelResults.forEach(({ toolCall, toolResult, rawResult }) => {
          toolResults.push(toolResult);
          
          // Collect successful images
          if (toolCall.function.name === 'generateSingleImage' && rawResult.success && rawResult.image) {
            generatedImages.push(rawResult.image);
          }
        });
        
        // Update final response with all results
        if (generatedImages.length > 0) {
          finalResponse.generatedImages = generatedImages;
          finalResponse.structuredOutput = {
            type: 'image_generation',
            data: {
              success: true,
              totalImages: generatedImages.length,
              images: generatedImages,
              parallelExecution: true
            }
          };
        }

        finalResponse.toolResults = toolResults;

        // Notify that images are ready for immediate display (before follow-up response)
        if (generatedImages.length > 0 && callbacks.onImagesReady) {
          callbacks.onImagesReady(generatedImages, toolResults);
        }

        // Get model's response to tool results by making another API call
        const updatedMessages = [...messages];
        
        // Add assistant's tool calls
        updatedMessages.push({
          role: 'assistant',
          content: null,
          tool_calls: Object.values(currentToolCalls)
        });
        
        // Add tool results
        for (let i = 0; i < Object.values(currentToolCalls).length; i++) {
          const toolCall = Object.values(currentToolCalls)[i] as any;
          const toolResult = toolResults[i];
          
          updatedMessages.push({
            role: 'tool',
            content: JSON.stringify(toolResult.data),
            tool_call_id: toolCall.id
          });
        }
        
        // Stream the model's final response to tool results
        const followUpStream = await openai.chat.completions.create({
          model: 'gpt-4.1',
          messages: updatedMessages,
          temperature: 0.7,
          max_tokens: 1500,
          stream: true
        });

        let followUpText = '';

        // Stream the follow-up response
        for await (const chunk of followUpStream) {
          const choice = chunk.choices[0];

          if (!choice) continue;

          // Handle text content streaming for follow-up response
          if (choice.delta.content) {
            followUpText += choice.delta.content;
            callbacks.onTextDelta(choice.delta.content, followUpText);
          }

          // Check if streaming is complete
          if (choice.finish_reason === 'stop') {
            break;
          }
        }

        finalResponse.message = followUpText || "I've completed your request.";
        finalResponse.toolResults = toolResults;
        
      } else {
        // No tool calls, just text response
        finalResponse.message = accumulatedText || "I'm here to help! What would you like to create today?";
      }

      // Notify completion
      callbacks.onComplete(finalResponse);

    } catch (error) {
      console.error('[OpenAIAgent] Streaming error:', error);
      callbacks.onError(error instanceof Error ? error : new Error('Unknown streaming error'));
    }
  }

  updateUserContext(userContext: UserContext): void {
    this.userContext = userContext;
  }
}
