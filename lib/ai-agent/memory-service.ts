/**
 * Memory Service - Server-side memory operations with proper authentication
 * 
 * Purpose: Provides memory operations that work in server-side contexts
 * Performance Notes: Direct Firestore access avoids API route overhead
 * Dependencies: Firebase Admin SDK, Clerk authentication
 */

import { getAdminDb } from '@/lib/firebaseAdmin';
import { Timestamp } from 'firebase-admin/firestore';

export interface UserMemory {
  id: string;
  content: string;
  category: string;
  context?: string;
  createdAt: string;
}

export interface MemorySearchResult {
  success: boolean;
  memories: UserMemory[];
  total: number;
  error?: string;
}

export interface MemorySaveResult {
  success: boolean;
  id?: string;
  error?: string;
}

/**
 * Server-side memory service that bypasses API authentication issues
 * Used by AI Agent for direct Firestore access
 */
export class MemoryService {
  private adminDb;

  constructor() {
    this.adminDb = getAdminDb();
  }

  /**
   * Search user memories directly from Firestore
   * @param userId - Clerk user ID
   * @param query - Search query string
   * @param category - Optional category filter
   * @param limit - Maximum results to return
   */
  async searchMemories(
    userId: string,
    query: string,
    category?: string | null,
    limit: number = 5
  ): Promise<MemorySearchResult> {
    try {
      console.log('[MemoryService] Searching memories for user:', userId, {
        query,
        category,
        limit
      });

      if (!userId) {
        return {
          success: false,
          memories: [],
          total: 0,
          error: 'User ID is required'
        };
      }

      // Build Firestore query
      let memoriesQuery = this.adminDb
        .collection('memories')
        .where('user_id', '==', userId);

      // Add category filter if specified
      if (category && category !== 'null') {
        memoriesQuery = memoriesQuery.where('category', '==', category);
      }

      // Order by creation date and limit
      memoriesQuery = memoriesQuery
        .orderBy('created_at', 'desc')
        .limit(Math.min(limit, 20));

      const memoriesSnap = await memoriesQuery.get();
      
      console.log('[MemoryService] Found', memoriesSnap.size, 'memories before filtering');

      // Convert to UserMemory objects
      let memories: UserMemory[] = memoriesSnap.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          content: data.content || '',
          category: data.category || 'general',
          context: data.context || '',
          createdAt: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString()
        };
      });

      // Apply text search filter if query provided
      if (query && query.trim()) {
        const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
        
        memories = memories.filter(memory => {
          const searchableText = (
            memory.content + ' ' + 
            memory.category + ' ' + 
            (memory.context || '')
          ).toLowerCase();
          
          // Memory matches if it contains any of the search terms
          return searchTerms.some(term => searchableText.includes(term));
        });
      }

      console.log('[MemoryService] Filtered to', memories.length, 'matching memories');

      return {
        success: true,
        memories,
        total: memories.length
      };

    } catch (error) {
      console.error('[MemoryService] Error searching memories:', error);
      
      return {
        success: false,
        memories: [],
        total: 0,
        error: error instanceof Error ? error.message : 'Failed to search memories'
      };
    }
  }

  /**
   * Save a memory directly to Firestore
   * @param userId - Clerk user ID
   * @param content - Memory content
   * @param category - Memory category
   * @param context - Optional context
   */
  async saveMemory(
    userId: string,
    content: string,
    category: string = 'general',
    context?: string
  ): Promise<MemorySaveResult> {
    try {
      console.log('[MemoryService] Saving memory for user:', userId, {
        contentLength: content.length,
        category,
        hasContext: !!context
      });

      if (!userId) {
        return {
          success: false,
          error: 'User ID is required'
        };
      }

      if (!content || content.trim().length === 0) {
        return {
          success: false,
          error: 'Content is required'
        };
      }

      if (content.length > 200) {
        return {
          success: false,
          error: 'Content must be 200 characters or less'
        };
      }

      // Verify user exists
      const userDocRef = this.adminDb.collection('users').doc(userId);
      const userSnap = await userDocRef.get();
      
      if (!userSnap.exists) {
        console.log('[MemoryService] User document not found:', userId);
        return {
          success: false,
          error: 'User not found'
        };
      }

      // Create memory document
      const memoryData = {
        user_id: userId,
        content: content.trim(),
        category: category || 'general',
        context: context?.trim() || '',
        created_at: Timestamp.now(),
        updated_at: Timestamp.now()
      };

      const memoryRef = await this.adminDb.collection('memories').add(memoryData);
      
      console.log('[MemoryService] Memory saved successfully:', memoryRef.id);

      return {
        success: true,
        id: memoryRef.id
      };

    } catch (error) {
      console.error('[MemoryService] Error saving memory:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save memory'
      };
    }
  }

  /**
   * Get recent memories for a user (used for welcome message personalization)
   * @param userId - Clerk user ID
   * @param limit - Number of recent memories to fetch
   */
  async getRecentMemories(userId: string, limit: number = 3): Promise<UserMemory[]> {
    try {
      if (!userId) {
        return [];
      }

      const result = await this.searchMemories(userId, 'recent preferences goals', null, limit);
      return result.memories;

    } catch (error) {
      console.error('[MemoryService] Error fetching recent memories:', error);
      return [];
    }
  }
}

// Singleton instance for server-side use
export const memoryService = new MemoryService();
