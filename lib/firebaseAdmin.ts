/**
 * Firebase Admin SDK Initialization
 *
 * This module initializes the Firebase Admin SDK for server-side operations,
 * providing access to Firestore, Auth, and Storage services. It loads
 * configuration from environment variables.
 *
 * Required environment variables:
 * - FIREBASE_PROJECT_ID: The Firebase project ID
 * - FIREBASE_CLIENT_EMAIL: Service account email
 * - FIREBASE_PRIVATE_KEY: Service account private key (with \n for newlines)
 * - FIREBASE_DATABASE_ID: The Firestore database ID (e.g. "market-me-db")
 * - (optionally) FIREBASE_STORAGE_BUCKET: If your Firebase bucket name differs
 *
 * To use:
 *  1. For Firestore operations, import { getAdminDb } from "@/lib/firebaseAdmin"
 *  2. For Auth operations, import { getAdminAuth } from "@/lib/firebaseAdmin"
 *  3. For Storage operations, import { getAdminStorage } from "@/lib/firebaseAdmin"
 */

import { initializeApp, cert, getApps, App } from "firebase-admin/app";
import { getFirestore, Firestore } from "firebase-admin/firestore";
import { getAuth, Auth } from "firebase-admin/auth";
import { getStorage, Storage } from "firebase-admin/storage";

let firebaseAdminApp: App;
let adminDb: Firestore;
let adminAuth: Auth;
let adminStorage: Storage;

/**
 * Validates that required Firebase environment variables exist
 */
const validateEnvVars = () => {
  const requiredVars = [
    "FIREBASE_PROJECT_ID",
    "FIREBASE_CLIENT_EMAIL",
    "FIREBASE_PRIVATE_KEY",
    "FIREBASE_DATABASE_ID",
  ];

  const missingVars = requiredVars.filter((varName) => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required Firebase environment variables: ${missingVars.join(", ")}`
    );
  }

  return true;
};

try {
  // Ensure environment variables are present
  validateEnvVars();

  // Only initialize if we haven't done so already
  if (!getApps().length) {
    console.log("[firebaseAdmin] Initializing new Admin App...");

    // Build service account object from environment
    const serviceAccount = {
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
    };

    // If you want to override the default bucket name (PROJECT_ID.appspot.com),
    // set the environment variable FIREBASE_STORAGE_BUCKET to your actual bucket name.
    // For example, if your console shows "market-me-5d80d.firebasestorage.app" as the bucket:
    const storageBucketName =
      process.env.FIREBASE_STORAGE_BUCKET ||
      "market-me-5d80d.firebasestorage.app"; // Explicitly set based on Firebase console

    console.log(`[firebaseAdmin] Using storage bucket: ${storageBucketName}`);

    // Initialize the Firebase Admin app
    firebaseAdminApp = initializeApp({
      credential: cert(serviceAccount),
      databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}.firebaseio.com`,
      storageBucket: storageBucketName,
    });

    console.log("[firebaseAdmin] Admin App initialized successfully");
  } else {
    console.log("[firebaseAdmin] Admin App already initialized, reusing existing app");
    firebaseAdminApp = getApps()[0];
  }

  // Firestore DB name
  const databaseId = process.env.FIREBASE_DATABASE_ID || "";
  if (!databaseId) {
    throw new Error("FIREBASE_DATABASE_ID environment variable is empty");
  }

  // Initialize Firestore
  adminDb = getFirestore(firebaseAdminApp, databaseId);
  console.log(`[firebaseAdmin] Connecting to Firestore database: ${databaseId}`);

  // Auth
  adminAuth = getAuth(firebaseAdminApp);

  // Storage
  adminStorage = getStorage(firebaseAdminApp);
  console.log("[firebaseAdmin] Firebase Storage initialized");

  // Simple Firestore operation to verify success
  console.log("[firebaseAdmin] Testing Firestore connection...");
} catch (error) {
  console.error("[firebaseAdmin] Error initializing Firebase Admin:", error);
  console.error(`
    [firebaseAdmin] Make sure your .env.local file contains the following variables:
    - FIREBASE_PROJECT_ID
    - FIREBASE_CLIENT_EMAIL
    - FIREBASE_PRIVATE_KEY
    - FIREBASE_DATABASE_ID
    (Optionally: FIREBASE_STORAGE_BUCKET if your bucket name is not PROJECT_ID.appspot.com)
  `);
}

/**
 * Return the Firestore instance or throw if not available
 */
export const getAdminDb = (): Firestore => {
  if (!adminDb) {
    throw new Error(
      "Firestore admin client is not initialized. Check your Firebase configuration."
    );
  }
  return adminDb;
};

/**
 * Return the Auth instance or throw if not available
 */
export const getAdminAuth = (): Auth => {
  if (!adminAuth) {
    throw new Error(
      "Firebase admin auth is not initialized. Check your Firebase configuration."
    );
  }
  return adminAuth;
};

/**
 * Return the Storage instance or throw if not available
 */
export const getAdminStorage = (): Storage => {
  if (!adminStorage) {
    throw new Error(
      "Firebase admin storage is not initialized. Check your Firebase configuration."
    );
  }
  return adminStorage;
};

// Export them if needed
export { adminDb, adminAuth, adminStorage };
