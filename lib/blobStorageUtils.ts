// lib/blobStorageUtils.ts
import { put, del } from "@vercel/blob";
import { v4 as uuidv4 } from "uuid";

// Generate a unique filename with timestamp
const generateUniqueFilename = () => {
  const timestamp = Date.now();
  return `${timestamp}-${uuidv4()}`;
};

/**
 * Upload a file from base64 data to Vercel Blob
 * @param base64String - The base64 string (with or without data URL prefix)
 * @param folderPath - Optional folder path within storage
 * @returns Promise with the download URL of the uploaded file
 */
export const uploadFileFromBase64 = async (
  base64String: string,
  folderPath: string = "images"
): Promise<string> => {
  try {
    console.log(`[blobStorageUtils] Uploading file from base64 to folder: ${folderPath}`);

    // Remove data URL prefix if present
    const base64Data = base64String.replace(/^data:image\/\w+;base64,/, "");
    
    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, "base64");
    
    // Create the file path with folder and unique filename (using .webp extension)
    const fileName = `${folderPath}/${generateUniqueFilename()}.webp`;
    
    // Upload to Vercel Blob with WebP content type
    const blob = await put(fileName, buffer, {
      access: "public",
      contentType: "image/webp",
    });

    console.log(`[blobStorageUtils] Successfully uploaded WebP file, URL: ${blob.url}`);
    return blob.url;
  } catch (error) {
    console.error("[blobStorageUtils] Error uploading file from base64:", error);
    throw error;
  }
};

/**
 * Upload a file buffer to Vercel Blob
 * @param buffer - The file buffer
 * @param fileName - The desired filename
 * @param contentType - The MIME type of the file
 * @param folderPath - Optional folder path within storage
 * @returns Promise with the download URL of the uploaded file
 */
export const uploadFileFromBuffer = async (
  buffer: Buffer,
  fileName: string,
  contentType: string = "image/webp",
  folderPath: string = "images"
): Promise<string> => {
  try {
    console.log(`[blobStorageUtils] Uploading file from buffer: ${fileName}`);

    // Create the file path with folder and filename
    const filePath = `${folderPath}/${fileName}`;
    
    // Upload to Vercel Blob
    const blob = await put(filePath, buffer, {
      access: "public",
      contentType,
    });

    console.log(`[blobStorageUtils] Successfully uploaded file, URL: ${blob.url}`);
    return blob.url;
  } catch (error) {
    console.error("[blobStorageUtils] Error uploading file from buffer:", error);
    throw error;
  }
};

/**
 * Delete a file from Vercel Blob by URL
 * @param url - The Vercel Blob URL of the file to delete
 */
export const deleteFileByUrl = async (url: string): Promise<void> => {
  try {
    if (!url || typeof url !== "string") {
      console.warn("[blobStorageUtils] Invalid URL provided for deletion:", url);
      return;
    }

    console.log(`[blobStorageUtils] Attempting to delete file: ${url}`);

    // For Vercel Blob, we can delete directly using the URL
    await del(url);
    
    console.log(`[blobStorageUtils] Successfully deleted file: ${url}`);
  } catch (error) {
    console.error("[blobStorageUtils] Error deleting file:", error);
    console.error("[blobStorageUtils] Continuing without failing the request");
  }
};

/**
 * Generate a unique file path for uploading
 * @param folderPath - The folder path
 * @param extension - File extension (default: .png)
 * @returns A unique file path
 */
export const generateUniqueFilePath = (
  folderPath: string = "images",
  extension: string = ".png"
): string => {
  const uniqueFileName = generateUniqueFilename();
  return `${folderPath}/${uniqueFileName}${extension}`;
};

/**
 * Extract the pathname from a Vercel Blob URL for operations like delete
 * @param url - The Vercel Blob URL
 * @returns The pathname or null if invalid
 */
export const extractPathnameFromBlobUrl = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    // For Vercel Blob URLs, the pathname typically contains the file path
    return urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
  } catch (error) {
    console.error("[blobStorageUtils] Error extracting pathname from URL:", error);
    return null;
  }
};