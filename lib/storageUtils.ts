// lib/storageUtils.ts
import { v4 as uuidv4 } from "uuid";
import fs from "fs/promises";
import path from "path";
import os from "os";
import { getAdminStorage } from "./firebaseAdmin";

// Generate a unique filename with timestamp
const generateUniqueFilename = () => {
  const timestamp = Date.now();
  return `${timestamp}-${uuidv4()}`;
};

/**
 * Upload a file from base64 data to Firebase Storage
 * @param base64String - The base64 string (with or without data URL prefix)
 * @param folderPath - Optional folder path within storage bucket
 * @returns Promise with the download URL of the uploaded file
 */
export const uploadFileFromBase64 = async (
  base64String: string,
  folderPath: string = "images"
): Promise<string> => {
  try {
    console.log(`[storageUtils] Uploading file from base64`);

    // Remove data URL prefix if present
    const base64Data = base64String.replace(/^data:image\/\w+;base64,/, "");

    // Create a temporary file path
    const tempFilePath = path.join(os.tmpdir(), `${generateUniqueFilename()}.png`);

    // Convert base64 to buffer and write to temp file
    await fs.writeFile(tempFilePath, Buffer.from(base64Data, "base64"));

    // Upload to Firebase Storage
    const storage = getAdminStorage();
    const fileName = `${folderPath}/${generateUniqueFilename()}.png`;
    const file = await storage.bucket().upload(tempFilePath, {
      destination: fileName,
      metadata: {
        contentType: "image/png"
      }
    });

    // Clean up the temporary file
    await fs.unlink(tempFilePath);

    // Get a public (signed) URL
    const [downloadUrl] = await file[0].getSignedUrl({
      action: "read",
      expires: "03-01-2500"
    });

    console.log(`[storageUtils] Successfully uploaded file, URL: ${downloadUrl}`);
    return downloadUrl;
  } catch (error) {
    console.error("[storageUtils] Error uploading file from base64:", error);
    throw error;
  }
};

/**
 * Delete a file from Firebase Storage by URL
 * @param url - The Firebase Storage URL of the file to delete
 */
export const deleteFileByUrl = async (url: string): Promise<void> => {
  try {
    if (!url || typeof url !== "string") {
      console.warn("[storageUtils] Invalid URL provided for deletion:", url);
      return;
    }

    console.log(`[storageUtils] Attempting to delete file: ${url}`);

    const storage = getAdminStorage();
    const bucket = storage.bucket();
    const urlObj = new URL(url);
    const pathName = urlObj.pathname;
    let filePath: string;

    // Parse the path from the known Firebase or GCS URL format
    if (pathName.includes("/o/")) {
      // Typical firebase link: /v0/b/PROJECT_ID.appspot.com/o/encodedPath
      const parts = pathName.split("/o/");
      if (parts.length < 2) {
        console.warn("[storageUtils] Invalid Firebase Storage URL format:", url);
        return;
      }
      filePath = decodeURIComponent(parts[1].split("?")[0]);
    } else {
      // Direct GCS link: /PROJECT_ID.appspot.com/whatever
      const bucketName = bucket.name;
      filePath = pathName.replace(`/${bucketName}/`, "");
    }

    const [exists] = await bucket.file(filePath).exists();
    if (!exists) {
      console.warn(`[storageUtils] File does not exist in storage: ${filePath}`);
      return;
    }

    await bucket.file(filePath).delete();
    console.log(`[storageUtils] Successfully deleted file: ${filePath}`);
  } catch (error) {
    console.error("[storageUtils] Error deleting file:", error);
    console.error("[storageUtils] Continuing without failing the request");
  }
};