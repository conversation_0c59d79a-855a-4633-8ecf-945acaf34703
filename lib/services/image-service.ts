/**
 * Centralized Image Service
 *
 * This service consolidates all image-related operations including:
 * - Blob URL to base64 conversion
 * - Image format validation
 * - MIME type detection
 * - Client-side image processing
 * 
 * Note: Server-side format conversion is handled by ImageConverter
 */

// OpenAI supported formats
const OPENAI_SUPPORTED_FORMATS = ['png', 'jpeg', 'jpg', 'webp', 'gif'];

export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  images?: string[];
}

export interface ImageValidationResult {
  valid: boolean;
  error?: string;
  format?: string;
}

export class ImageService {
  /**
   * Convert blob URLs in conversation history to base64 data URLs
   * This is necessary for OpenAI API compatibility which doesn't support blob URLs
   */
  static async convertBlobUrlsInHistory(
    conversationHistory: ConversationMessage[]
  ): Promise<ConversationMessage[]> {
    console.log('[ImageService] Converting blob URLs in conversation history...');
    const convertedHistory: ConversationMessage[] = [];
    
    for (const msg of conversationHistory) {
      if (msg.images && msg.images.length > 0) {
        const convertedImages: string[] = [];
        
        for (const imageUrl of msg.images) {
          if (imageUrl.startsWith('blob:')) {
            console.log('[ImageService] WARNING: Found blob URL in conversation history - attempting conversion:', imageUrl.substring(0, 50) + '...');
            try {
              const dataUrl = await this.convertBlobToBase64(imageUrl);
              convertedImages.push(dataUrl);
            } catch (error) {
              console.error('[ImageService] Failed to convert blob URL:', error);
              console.log('[ImageService] Skipping invalid blob URL:', imageUrl.substring(0, 50) + '...');
              // Skip this image if conversion fails
            }
          } else if (imageUrl.startsWith('data:')) {
            convertedImages.push(imageUrl);
          } else {
            console.log('[ImageService] WARNING: Unknown image URL format:', imageUrl.substring(0, 50) + '...');
            // Skip unknown formats
          }
        }
        
        convertedHistory.push({
          ...msg,
          images: convertedImages.length > 0 ? convertedImages : undefined
        });
      } else {
        convertedHistory.push(msg);
      }
    }
    
    console.log('[ImageService] Blob URL conversion completed:', {
      originalMessages: conversationHistory.length,
      convertedMessages: convertedHistory.length,
      messagesWithImages: convertedHistory.filter(msg => msg.images && msg.images.length > 0).length
    });
    
    return convertedHistory;
  }

  /**
   * Convert a single blob URL to base64 data URL
   */
  static async convertBlobToBase64(blobUrl: string): Promise<string> {
    try {
      console.log('[ImageService] Converting blob URL:', blobUrl);
      
      const response = await fetch(blobUrl);
      const blob = await response.blob();
      
      console.log('[ImageService] Blob info:', {
        size: blob.size,
        type: blob.type,
        originalType: blob.type
      });
      
      // Detect MIME type from file content if missing or generic
      let correctedMimeType = blob.type;
      if (!blob.type || blob.type === 'application/octet-stream' || !blob.type.startsWith('image/')) {
        correctedMimeType = await this.detectImageMimeType(blob);
        console.log('[ImageService] Detected MIME type:', correctedMimeType);
      }
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          let result = reader.result as string;
          console.log('[ImageService] Generated data URL:', {
            prefix: result.substring(0, 50) + '...',
            length: result.length,
            mimeType: result.match(/^data:([^;]+)/)?.[1]
          });
          
          // Fix MIME type in data URL if needed
          const currentMimeType = result.match(/^data:([^;]+)/)?.[1];
          if (!currentMimeType || currentMimeType === 'application/octet-stream' || !currentMimeType.startsWith('image/')) {
            console.log('[ImageService] Correcting MIME type in data URL from', currentMimeType, 'to', correctedMimeType);
            const base64Data = result.split(',')[1];
            result = `data:${correctedMimeType};base64,${base64Data}`;
          }
          
          // Final validation
          if (!result.startsWith('data:image/')) {
            console.error('[ImageService] Invalid data URL format after correction:', result.substring(0, 100));
            reject(new Error('Failed to generate valid image data URL'));
            return;
          }
          
          console.log('[ImageService] Final data URL:', {
            prefix: result.substring(0, 50) + '...',
            length: result.length,
            mimeType: result.match(/^data:([^;]+)/)?.[1]
          });
          
          resolve(result);
        };
        reader.onerror = (error) => {
          console.error('[ImageService] FileReader error:', error);
          reject(error);
        };
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('[ImageService] Error converting blob to base64:', error);
      throw error;
    }
  }

  /**
   * Detect image MIME type from file content using magic bytes
   */
  static async detectImageMimeType(blob: Blob): Promise<string> {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        const buffer = new Uint8Array(reader.result as ArrayBuffer);
        
        if (buffer.length < 4) {
          resolve('application/octet-stream');
          return;
        }
        
        // PNG: 89 50 4E 47
        if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
          resolve('image/png');
          return;
        }
        
        // JPEG: FF D8 FF
        if (buffer[0] === 0xFF && buffer[1] === 0xD8 && buffer[2] === 0xFF) {
          resolve('image/jpeg');
          return;
        }
        
        // GIF: 47 49 46 38
        if (buffer[0] === 0x47 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x38) {
          resolve('image/gif');
          return;
        }
        
        // WEBP: 52 49 46 46 (RIFF) at start and 57 45 42 50 (WEBP) at offset 8
        if (buffer.length >= 12 &&
            buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46 &&
            buffer[8] === 0x57 && buffer[9] === 0x45 && buffer[10] === 0x42 && buffer[11] === 0x50) {
          resolve('image/webp');
          return;
        }
        
        // Default fallback
        resolve('image/jpeg');
      };
      
      reader.onerror = () => resolve('image/jpeg');
      reader.readAsArrayBuffer(blob.slice(0, 12));
    });
  }

  /**
   * Validate image URL format and ensure it's a supported format
   */
  static validateImageUrl(imageUrl: string): ImageValidationResult {
    if (!imageUrl.startsWith('data:image/')) {
      return {
        valid: false,
        error: 'Invalid image format - must be a data URL'
      };
    }
    
    const formatMatch = imageUrl.match(/^data:image\/([^;]+)/);
    if (!formatMatch) {
      return {
        valid: false,
        error: 'Invalid image URL format'
      };
    }
    
    const imageFormat = formatMatch[1];
    const supportedFormats = ['png', 'jpeg', 'jpg', 'webp', 'gif'];
    
    if (!supportedFormats.includes(imageFormat.toLowerCase())) {
      return {
        valid: false,
        error: `Unsupported image format: ${imageFormat}. Supported formats: ${supportedFormats.join(', ')}`,
        format: imageFormat
      };
    }
    
    // Check if the data URL has actual content
    if (!imageUrl.includes(',') || imageUrl.split(',')[1].length === 0) {
      return {
        valid: false,
        error: 'Invalid data URL - missing image data'
      };
    }
    
    return {
      valid: true,
      format: imageFormat
    };
  }

  /**
   * Extract format from data URL
   */
  static extractFormat(dataUrl: string): string | null {
    const formatMatch = dataUrl.match(/^data:image\/([^;]+)/);
    return formatMatch ? formatMatch[1].toLowerCase() : null;
  }

  /**
   * Check if an image format is supported by OpenAI
   */
  static isOpenAISupported(format: string): boolean {
    return OPENAI_SUPPORTED_FORMATS.includes(format.toLowerCase());
  }

  /**
   * Convert an array of image URLs, handling blob URLs
   * Note: Format conversion happens server-side
   */
  static async processImageUrls(imageUrls: string[]): Promise<string[]> {
    const processedImages: string[] = [];
    
    for (const imageUrl of imageUrls) {
      try {
        if (imageUrl.startsWith('blob:')) {
          const dataUrl = await this.convertBlobToBase64(imageUrl);
          processedImages.push(dataUrl);
        } else if (imageUrl.startsWith('data:')) {
          // Basic validation for data URLs
          const validation = this.validateImageUrl(imageUrl);
          if (validation.valid) {
            processedImages.push(imageUrl);
          } else {
            // Still include it - server-side conversion will handle unsupported formats
            console.log('[ImageService] Including potentially unsupported format for server-side conversion');
            processedImages.push(imageUrl);
          }
        } else {
          console.warn('[ImageService] Skipping unsupported URL format:', imageUrl.substring(0, 50));
        }
      } catch (error) {
        console.error('[ImageService] Error processing image URL:', error);
        // Continue with other images
      }
    }
    
    return processedImages;
  }
}
