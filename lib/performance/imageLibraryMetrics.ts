import { useMemo } from 'react';

// Performance monitoring for Image Library
export class ImageLibraryMetrics {
  private static instance: ImageLibraryMetrics;
  private metrics: Map<string, number> = new Map();
  private loadStartTime: number = 0;

  static getInstance(): ImageLibraryMetrics {
    if (!ImageLibraryMetrics.instance) {
      ImageLibraryMetrics.instance = new ImageLibraryMetrics();
    }
    return ImageLibraryMetrics.instance;
  }

  startImageLibraryLoad(): void {
    this.loadStartTime = performance.now();
    this.metrics.set('loadStart', this.loadStartTime);
  }

  recordFirstImageLoad(): void {
    const firstImageTime = performance.now() - this.loadStartTime;
    this.metrics.set('firstImageLoad', firstImageTime);
    console.log(`[Performance] First image loaded in: ${firstImageTime.toFixed(2)}ms`);
  }

  recordAllImagesLoaded(count: number): void {
    const allImagesTime = performance.now() - this.loadStartTime;
    this.metrics.set('allImagesLoaded', allImagesTime);
    this.metrics.set('imageCount', count);
    console.log(`[Performance] All ${count} images loaded in: ${allImagesTime.toFixed(2)}ms`);
  }

  recordAPIResponse(responseTime: number, imageCount: number): void {
    this.metrics.set('apiResponseTime', responseTime);
    this.metrics.set('apiImageCount', imageCount);
    console.log(`[Performance] API responded in: ${responseTime.toFixed(2)}ms with ${imageCount} images`);
  }

  recordMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.set('memoryUsed', memory.usedJSHeapSize);
      this.metrics.set('memoryTotal', memory.totalJSHeapSize);
      console.log(`[Performance] Memory: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB used`);
    }
  }

  getMetrics(): Record<string, number> {
    const result: Record<string, number> = {};
    this.metrics.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  logPerformanceSummary(): void {
    const metrics = this.getMetrics();
    console.group('[Performance Summary] Image Library');
    console.log('📊 Load Metrics:');
    console.log(`  • First Image: ${metrics.firstImageLoad?.toFixed(2) || 'N/A'}ms`);
    console.log(`  • All Images: ${metrics.allImagesLoaded?.toFixed(2) || 'N/A'}ms`);
    console.log(`  • API Response: ${metrics.apiResponseTime?.toFixed(2) || 'N/A'}ms`);
    console.log(`  • Image Count: ${metrics.imageCount || metrics.apiImageCount || 'N/A'}`);
    
    if (metrics.memoryUsed) {
      console.log(`💾 Memory Usage: ${(metrics.memoryUsed / 1024 / 1024).toFixed(2)}MB`);
    }
    
    // Performance indicators
    const firstImageTime = metrics.firstImageLoad;
    if (firstImageTime) {
      if (firstImageTime < 500) {
        console.log('🚀 Excellent performance (< 500ms)');
      } else if (firstImageTime < 1000) {
        console.log('✅ Good performance (< 1s)');
      } else if (firstImageTime < 2000) {
        console.log('⚠️ Average performance (< 2s)');
      } else {
        console.log('❌ Poor performance (> 2s)');
      }
    }
    console.groupEnd();
  }

  reset(): void {
    this.metrics.clear();
    this.loadStartTime = 0;
  }
}

// Hook for using performance metrics in React components
export function useImageLibraryMetrics() {
  const metrics = ImageLibraryMetrics.getInstance();

  // Memoize the returned object to prevent recreation on every render
  // Include metrics dependency to fix exhaustive-deps warning
  return useMemo(() => ({
    startLoad: () => metrics.startImageLibraryLoad(),
    recordFirstImage: () => metrics.recordFirstImageLoad(),
    recordAllImages: (count: number) => metrics.recordAllImagesLoaded(count),
    recordAPI: (time: number, count: number) => metrics.recordAPIResponse(time, count),
    recordMemory: () => metrics.recordMemoryUsage(),
    getMetrics: () => metrics.getMetrics(),
    logSummary: () => metrics.logPerformanceSummary(),
    reset: () => metrics.reset()
  }), [metrics]); // Include metrics in dependency array
}