{"name": "market-me", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.12.12", "@fal-ai/client": "^1.3.0", "@google/generative-ai": "^0.24.0", "@llm-ui/code": "^0.13.3", "@llm-ui/markdown": "^0.13.3", "@llm-ui/react": "^0.13.3", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase-admin": "^13.3.0", "html-react-parser": "^5.2.5", "jszip": "^3.10.1", "lucide-react": "^0.486.0", "next": "15.2.4", "next-themes": "^0.4.6", "openai": "^4.96.2", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "resend": "^4.2.0", "sharp": "^0.34.1", "shiki": "^3.4.2", "sonner": "^2.0.3", "stripe": "^18.0.0", "svix": "^1.63.0", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}