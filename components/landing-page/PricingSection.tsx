"use client";

import React from 'react';
import { Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface SubscriptionPlan {
  id: string;
  name: string;
  monthlyPrice: number;
  monthlyCredits: number;
  costPerCredit: number;
  features: string[];
  mostPopular?: boolean;
}

const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: "basic",
    name: "Basic",
    monthlyPrice: 49,
    monthlyCredits: 50,
    costPerCredit: 0.98,
    features: [
      "50 AI-generated images per month",
      "All aspect ratios",
      "Multiple model types & ethnicities",
      "Custom backgrounds",
      "Batch generation (up to 8 at once)",
      "Never-expiring credits",
      "Cancel anytime"
    ]
  },
  {
    id: "growth",
    name: "Growth",
    monthlyPrice: 199,
    monthlyCredits: 230,
    costPerCredit: 0.87,
    features: [
      "230 AI-generated images per month",
      "All aspect ratios",
      "Multiple model types & ethnicities",
      "Custom backgrounds",
      "Batch generation (up to 8 at once)",
      "Never-expiring credits",
      "Cancel anytime"
    ],
    mostPopular: true
  },
  {
    id: "pro",
    name: "Pro",
    monthlyPrice: 399,
    monthlyCredits: 480,
    costPerCredit: 0.83,
    features: [
      "480 AI-generated images per month",
      "All aspect ratios",
      "Multiple model types & ethnicities",
      "Custom backgrounds",
      "Batch generation (up to 8 at once)",
      "Never-expiring credits",
      "Cancel anytime"
    ]
  }
];

export function PricingSection() {
  // Calculate savings compared to traditional photoshoots
  const calculateSavings = (credits: number) => {
    // Average cost per image in traditional photoshoots ($87 for mass market)
    const traditionalCostPerImage = 87;
    const traditionalTotal = credits * traditionalCostPerImage;
    const ourTotal = credits * 1; // Our price is roughly $1 per image
    
    return Math.round((traditionalTotal - ourTotal) / traditionalTotal * 100);
  };

  return (
    <section id="pricing" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Simple, Subscription Pricing</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Monthly credits that never expire - build your credit balance over time
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {subscriptionPlans.map((plan) => (
            <div 
              key={plan.id} 
              className={`w-full border rounded-xl p-6 relative ${
                plan.mostPopular 
                  ? 'border-[#FD2D55] shadow-lg' 
                  : 'border-gray-200 shadow-sm hover:shadow-md transition-shadow'
              }`}
            >
              {plan.mostPopular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-[#FD2D55] text-white px-4 py-1 rounded-full text-sm font-medium whitespace-nowrap z-10">
                  Most Popular
                </div>
              )}
              
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold">${plan.monthlyPrice}</div>
                <div className="text-sm text-gray-500 mt-1">per month</div>
              </div>
              
              <div className="border-t border-gray-100 pt-4 mb-6">
                <div className="text-center mb-4">
                  <span className="font-semibold text-lg">{plan.monthlyCredits.toLocaleString()} credits/month</span>
                  <p className="text-gray-500 text-sm">${plan.costPerCredit.toFixed(2)} per image</p>
                </div>
                
                <ul className="space-y-3">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <div className="mr-2 mt-1">
                        <Check className="h-4 w-4 text-[#FD2D55]" />
                      </div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                  <li className="flex items-start">
                    <div className="mr-2 mt-1">
                      <Check className="h-4 w-4 text-[#FD2D55]" />
                    </div>
                    <span className="text-gray-600 text-sm">{calculateSavings(plan.monthlyCredits)}% savings vs. traditional photoshoots</span>
                  </li>
                </ul>
              </div>
              
              <Button 
                asChild
                className={`w-full rounded-full ${
                  plan.mostPopular 
                    ? 'bg-[#FD2D55] hover:bg-[#FD2D55]/90 text-white' 
                    : 'bg-white border border-gray-300 text-gray-800 hover:bg-gray-50'
                }`}
              >
                <Link href="/sign-up">
                  Subscribe Now
                </Link>
              </Button>
            </div>
          ))}
        </div>
        
        <div className="mt-10 text-center text-gray-600">
          <p>Secure payment processing by Stripe. All prices in USD.</p>
          <p>Credits never expire and accumulate each month. Cancel anytime.</p>
        </div>
      </div>
    </section>
  );
}