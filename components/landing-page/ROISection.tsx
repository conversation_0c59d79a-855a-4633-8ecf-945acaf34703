"use client";

import React from 'react';
import { DollarSign, TrendingUp, Clock } from 'lucide-react';

export function ROISection() {
  return (
    <section id="about" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">The Fashion Industry&apos;s Cost Problem</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Traditional fashion photography is expensive, slow, and limited in scale
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
          <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <DollarSign className="text-[#FD2D55]" />
              Traditional Fashion Shooting Costs
            </h3>
            
            <div className="overflow-x-auto">
              <table className="w-full text-left">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="pb-2 pr-2 font-medium text-gray-500">Cost Element</th>
                    <th className="pb-2 px-2 font-medium text-gray-500">Mass Fashion</th>
                    <th className="pb-2 px-2 font-medium text-gray-500">Luxury</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  <tr>
                    <td className="py-3 pr-2">Photographers & Assistants</td>
                    <td className="py-3 px-2">$40,000</td>
                    <td className="py-3 px-2">$33,000</td>
                  </tr>
                  <tr>
                    <td className="py-3 pr-2">Models & Talent</td>
                    <td className="py-3 px-2">$30,000</td>
                    <td className="py-3 px-2">$49,000</td>
                  </tr>
                  <tr>
                    <td className="py-3 pr-2">Set, Studio, Equipment</td>
                    <td className="py-3 px-2">$40,000</td>
                    <td className="py-3 px-2">$22,000</td>
                  </tr>
                  <tr>
                    <td className="py-3 pr-2">Daily Cost</td>
                    <td className="py-3 px-2 font-bold">$9,200</td>
                    <td className="py-3 px-2 font-bold">$24,500</td>
                  </tr>
                  <tr>
                    <td className="py-3 pr-2">Cost per Image</td>
                    <td className="py-3 px-2 font-bold">$87</td>
                    <td className="py-3 px-2 font-bold">$557</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <div className="flex flex-col gap-8">
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
              <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
                <TrendingUp className="text-[#FD2D55]" />
                Market-Me Solution
              </h3>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-xl font-bold text-[#FD2D55]">$0.98</p>
                    <p className="text-gray-600 text-sm">Basic plan cost per image</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-xl font-bold text-[#FD2D55]">$0.87</p>
                    <p className="text-gray-600 text-sm">Growth plan cost per image</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-xl font-bold text-[#FD2D55]">$0.77</p>
                    <p className="text-gray-600 text-sm">Enterprise plan cost per image</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-3xl font-bold">99%</p>
                    <p className="text-gray-600 text-sm">Cost savings vs. mass fashion</p>
                    <p className="text-xs text-gray-500 mt-1">$87 → $0.87</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-3xl font-bold">99.8%</p>
                    <p className="text-gray-600 text-sm">Cost savings vs. luxury brands</p>
                    <p className="text-xs text-gray-500 mt-1">$557 → $0.87</p>
                  </div>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="font-medium">Cost Comparison for 100 Images</p>
                  <table className="w-full mt-2 text-sm">
                    <tbody>
                      <tr className="border-b border-gray-200">
                        <td className="py-1">Traditional Mass Fashion</td>
                        <td className="py-1 text-right">$8,700</td>
                      </tr>
                      <tr className="border-b border-gray-200">
                        <td className="py-1">Traditional Luxury</td>
                        <td className="py-1 text-right">$55,700</td>
                      </tr>
                      <tr>
                        <td className="py-1 font-medium">Market-Me (Growth Plan)</td>
                        <td className="py-1 text-right font-medium text-[#FD2D55]">$87</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
              <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
                <Clock className="text-[#FD2D55]" />
                Time Efficiency
              </h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Traditional Photoshoots</span>
                  <span>2-4 weeks</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div className="h-2 w-[98%] bg-gray-400 rounded-full"></div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="font-medium">Market-Me AI Generation</span>
                  <span>2-3 minutes</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div className="h-2 w-[2%] bg-[#FD2D55] rounded-full"></div>
                </div>
                
                <p className="text-gray-600 mt-4">
                  Reduce time-to-market from weeks to minutes, allowing for rapid iteration and testing
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}