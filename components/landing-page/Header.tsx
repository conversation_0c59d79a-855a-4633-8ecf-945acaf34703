"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface HeaderProps {
  className?: string;
}

const menuItems = [
  { label: "Features", href: "#features" },
  { label: "How It Works", href: "#how-it-works" },
  { label: "Pricing", href: "#pricing" },
  { label: "About", href: "#about" }
];

export function Header({ className }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      <header
        className={cn(
          "fixed top-6 left-1/2 -translate-x-1/2 z-50 w-[98%] max-w-7xl transition-all duration-300",
          isScrolled ? "" : "bg-transparent",
          className
        )}
      >
        <div className={cn(
          "rounded-full transition-all duration-300",
          isScrolled || isMenuOpen ? "bg-white/95 backdrop-blur-sm shadow-lg border border-grey-200 hover:shadow-xl" : "bg-transparent"
        )}>
          <div className="h-14 flex items-center justify-between px-4 md:px-0">
            {/* Spacer for mobile to push logo to center */}
            <div className="w-6 md:hidden"></div> 
            
            {/* Logo - Centered on mobile */}
            <div className="flex-1 flex justify-center md:justify-start md:pl-8">
              <Link 
                href="/" 
                className={cn(
                  "transition-colors",
                  isScrolled || isMenuOpen ? "text-[#FD2D55] hover:text-[#FD2D55]/80" : "text-white hover:text-white/80"
                )}
              >
                <span className="text-2xl font-bold">MARKET-ME</span>
              </Link>
            </div>
            
            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center gap-6">
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "text-base font-medium tracking-wide transition-colors",
                    isScrolled 
                      ? "text-gray-800 hover:text-[#FD2D55]" 
                      : "text-white hover:text-white/80"
                  )}
                >
                  {item.label}
                </Link>
              ))}
            </nav>

            {/* Desktop Actions */}
            <div className="hidden md:flex items-center gap-2 pl-4 pr-4">
              <Link href="/sign-in">
                <Button 
                  variant="outline"
                  size="default"
                  className={cn(
                    "rounded-full text-sm font-semibold border-2",
                    isScrolled 
                      ? "border-gray-200 text-gray-800 hover:bg-gray-100" 
                      : "border-white text-white bg-black/30 hover:bg-black/40"
                  )}
                >
                  Sign In
                </Button>
              </Link>

              <Link href="/sign-up">
                <Button 
                  variant="default"
                  size="default"
                  className="rounded-full bg-[#FD2D55] hover:bg-[#FD2D55]/90 text-white hover:shadow-md transition-all text-sm font-semibold"
                >
                  Get Started
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={cn(
                "md:hidden transition-colors w-6",
                isScrolled || isMenuOpen ? "text-gray-800 hover:text-[#FD2D55]" : "text-white hover:text-white/80"
              )}
            >
              <Menu size={24} />
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          {/* Dark overlay */}
          <div 
            className="absolute inset-0 bg-black/80 backdrop-blur-sm"
            onClick={() => setIsMenuOpen(false)}
          />
          
          {/* Menu content */}
          <div className="relative h-screen flex flex-col pt-20">
            {/* Close Button */}
            <button 
              onClick={() => setIsMenuOpen(false)} 
              className="absolute top-6 right-6 text-white hover:text-[#FD2D55] transition-colors z-50"
            >
              <X size={28} />
            </button>
            
            {/* Navigation Links container */}
            <div className="flex-1 px-6 pt-8">
              <nav className="flex flex-col space-y-6 items-center text-center">
                {menuItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="text-2xl font-medium transition-colors text-white hover:text-[#FD2D55]"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                ))}
              </nav>
            </div>

            {/* Mobile Actions */}
            <div className="px-6 pb-8 space-y-4 mt-auto">
              <Link href="/sign-in" className="block">
                <Button 
                  variant="outline"
                  className="w-full h-12 rounded-full border-2 border-white text-white bg-black/30 hover:bg-black/40"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Sign In
                </Button>
              </Link>
              <Link href="/sign-up" className="block">
                <Button 
                  variant="default"
                  className="w-full h-12 rounded-full bg-[#FD2D55] hover:bg-[#FD2D55]/90 text-white"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}
    </>
  );
}