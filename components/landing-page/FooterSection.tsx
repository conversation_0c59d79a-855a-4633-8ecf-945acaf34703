import React from 'react';
import Link from 'next/link';

export function FooterSection() {
  return (
    <footer className="bg-white border-t border-gray-200 py-12">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-2xl font-bold mb-4" style={{ color: '#FD2D55' }}>MARKET-ME</h3>
            <p className="text-gray-600">Scale Your Style: 1000x More Content, 1000x Less Cost</p>
          </div>
          
          <div>
            <h4 className="font-bold mb-4">Product</h4>
            <ul className="space-y-2">
              <li><Link href="#features" className="text-gray-600 hover:text-[#FD2D55]">Features</Link></li>
              <li><Link href="#how-it-works" className="text-gray-600 hover:text-[#FD2D55]">How It Works</Link></li>
              <li><Link href="#pricing" className="text-gray-600 hover:text-[#FD2D55]">Pricing</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-bold mb-4">Company</h4>
            <ul className="space-y-2">
              <li><Link href="#about" className="text-gray-600 hover:text-[#FD2D55]">About Us</Link></li>
              <li><Link href="#" className="text-gray-600 hover:text-[#FD2D55]">Contact</Link></li>
              <li><Link href="#" className="text-gray-600 hover:text-[#FD2D55]">Privacy Policy</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-bold mb-4">Get Started</h4>
            <ul className="space-y-2">
              <li><Link href="/sign-up" className="text-gray-600 hover:text-[#FD2D55]">Sign Up</Link></li>
              <li><Link href="/sign-in" className="text-gray-600 hover:text-[#FD2D55]">Log In</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="mt-12 pt-8 border-t border-gray-100 text-center text-gray-500">
          <p>&copy; {new Date().getFullYear()} Market-Me. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}