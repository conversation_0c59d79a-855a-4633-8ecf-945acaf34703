"use client";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "./Header";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

interface HeroSectionProps {
  className?: string;
}

export function HeroSection({
  className
}: HeroSectionProps) {
  return (
    <section className={cn("relative min-h-screen", className)}>
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <img
          src="/landing-page/hero/gucci_tee_AI.webp"
          alt="Hero background - AI Generated Fashion Shoot"
          className="object-cover w-full h-full"
        />
        <div className="absolute inset-0 bg-black/30" />
      </div>

      {/* Header */}
      <Header className="z-20" />

      {/* Content Container: Made relative, removed justify-center, adjusted padding */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 text-white h-full flex flex-col items-center min-h-screen pt-24 pb-32 sm:pb-20">
        {/* Text Block: More margin on mobile, responsive spacing */}
        <div className="max-w-3xl text-center mt-48 sm:mt-32 w-full">
          <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold tracking-tight mb-6 leading-none">
            Fashion shoots,<br />
            without the shoot
          </h1>
          <p className="text-lg sm:text-lg md:text-xl lg:text-2xl mb-8 text-grey-100 leading-relaxed px-2 sm:px-4 max-w-full lg:whitespace-nowrap">
            Generate professional fashion marketing content <span className="inline sm:inline">at scale for a fraction of the cost</span>
          </p>
        </div>
        {/* Button Container: Absolute position on mobile, relative on sm+. Adjusted bottom padding for mobile */}
        <div className="absolute bottom-0 left-0 right-0 pb-28 sm:relative sm:bottom-auto sm:left-auto sm:right-auto sm:pb-0 sm:mt-60 flex flex-col sm:flex-row gap-4 items-center justify-center w-full">
          {/* Buttons have consistent width */}
          <Link 
            href="/sign-up"
            className="w-full sm:w-auto h-[48px] px-[24px] py-[12px] bg-[#FD2D55] text-white rounded-full text-[18px] font-medium hover:bg-[#FD2D55]/90 transition-colors inline-flex items-center justify-center max-w-xs mx-auto sm:mx-0"
          >
            Get Started
          </Link>
          {/* Added w-full for mobile width, sm:w-auto for desktop */}
          <Link 
            href="#how-it-works"
            className="w-full sm:w-auto h-[48px] px-[24px] py-[12px] border-2 border-white text-white rounded-full text-[18px] font-medium hover:bg-white/10 transition-colors inline-flex items-center justify-center gap-2 max-w-xs mx-auto sm:mx-0"
          >
            Learn More <ArrowRight size={18} />
          </Link>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="w-8 h-12 border-2 border-white rounded-full flex items-start justify-center p-2">
          <div className="w-1 h-3 bg-white rounded-full animate-bounce" />
        </div>
      </div>
    </section>
  );
}
