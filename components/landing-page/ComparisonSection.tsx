"use client";

import React, { useState } from 'react';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface ComparisonItem {
  brand: string;
  originalSrc: string;
  aiSrc: string;
  label: string;
}

const comparisonItems: ComparisonItem[] = [
  {
    brand: "Dior",
    originalSrc: "/landing-page/showcase-images/dior-showcase/dior_bag.webp",
    aiSrc: "/landing-page/showcase-images/dior-showcase/dior_bag_AI.webp",
    label: "Luxury Handbag"
  },
  {
    brand: "Dolce & Gabbana",
    originalSrc: "/landing-page/showcase-images/dolce-gabbana-showcase/dolce_dress.jpg", 
    aiSrc: "/landing-page/showcase-images/dolce-gabbana-showcase/dolce_dress_AI.webp",
    label: "Designer Dress"
  },
  {
    brand: "Zara",
    originalSrc: "/landing-page/showcase-images/zara-showcase/zara_jacket.jpg",
    aiSrc: "/landing-page/showcase-images/zara-showcase/zara_jacket_AI.webp",
    label: "Modern Jacket"
  },
  {
    brand: "Diesel",
    originalSrc: "/landing-page/showcase-images/diesel-showcase/diesel_bag.webp",
    aiSrc: "/landing-page/showcase-images/diesel-showcase/diesel_bag_AI.webp",
    label: "Casual Accessory"
  }
];

export function ComparisonSection() {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  
  return (
    <section id="how-it-works" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">From Product to Campaign</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Upload your product images and watch as our AI transforms them into professional marketing content in seconds
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-20">
          {comparisonItems.map((item, index) => (
            <div 
              key={index} 
              className="flex flex-col space-y-6"
              onMouseEnter={() => setActiveIndex(index)}
              onMouseLeave={() => setActiveIndex(null)}
            >
              <div 
                className={`grid grid-cols-2 gap-4 h-96 relative rounded-lg overflow-hidden border border-gray-100 shadow-sm ${
                  activeIndex === index ? 'shadow-md' : ''
                }`}
              >
                <div className="absolute top-0 left-0 z-10 m-3 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                  Original Product
                </div>
                <div className="absolute top-0 right-0 z-10 m-3 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                  AI Generated
                </div>
                
                <div className="h-full overflow-hidden">
                  <img 
                    src={item.originalSrc} 
                    alt={`${item.brand} original product`}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                <div className="h-full overflow-hidden">
                  <img 
                    src={item.aiSrc} 
                    alt={`${item.brand} AI generated marketing image`}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-full w-0.5 bg-white/80 shadow-md"></div>
                </div>
                
                <div 
                  className={`absolute inset-0 bg-gradient-to-tr from-black/20 to-transparent opacity-0 transition-opacity duration-300 ${
                    activeIndex === index ? 'opacity-100' : ''
                  }`}
                ></div>
              </div>
              
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-1">{item.brand} {item.label}</h3>
                <p className="text-gray-600">Transformed into a professional marketing campaign</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <Button 
            asChild
            className="rounded-full bg-[#FD2D55] hover:bg-[#FD2D55]/90 text-white px-8 py-3 text-lg"
          >
            <Link href="/sign-up">
              Start Creating Now <ArrowRight className="ml-2" size={16} />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}