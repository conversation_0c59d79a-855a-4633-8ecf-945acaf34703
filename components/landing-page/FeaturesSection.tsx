"use client";

import React from 'react';
import { Scale, Clock, Image, Palette, Sliders, Layers } from 'lucide-react';

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const features: Feature[] = [
  {
    icon: <Scale className="w-10 h-10 text-[#FD2D55]" />,
    title: "1000x Cost Reduction",
    description: "Slash campaign costs by up to 99.8% compared to traditional photoshoots, saving thousands of dollars per campaign"
  },
  {
    icon: <Clock className="w-10 h-10 text-[#FD2D55]" />,
    title: "Minutes, Not Weeks",
    description: "Generate professional marketing content in 2-3 minutes instead of spending weeks on production planning and execution"
  },
  {
    // eslint-disable-next-line jsx-a11y/alt-text
    icon: <Image className="w-10 h-10 text-[#FD2D55]" />,
    title: "Unlimited Scale",
    description: "Create thousands of images with endless variations for all your marketing channels without increasing costs"
  },
  {
    icon: <Palette className="w-10 h-10 text-[#FD2D55]" />,
    title: "Brand Consistency",
    description: "Maintain perfect brand consistency across all generated content with customizable styles and settings"
  },
  {
    icon: <Sliders className="w-10 h-10 text-[#FD2D55]" />,
    title: "Complete Customization",
    description: "Control every aspect from model type and ethnicity to background settings and aspect ratios for perfect brand alignment"
  },
  {
    icon: <Layers className="w-10 h-10 text-[#FD2D55]" />,
    title: "Batch Generation",
    description: "Create up to 8 unique variations in one go, giving you more options to choose from in a single generation"
  }
];

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Transform Your Fashion Marketing</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Market-Me revolutionizes how fashion brands create marketing content
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
            >
              <div className="mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}