# Vercel Blob

Vercel Blob is available on [all plans](/docs/plans)

Those with the [owner, member, developer](/docs/rbac/access-roles#owner, member, developer-role) role can access this feature

## [Use cases](#use-cases)

[Vercel Blob](/storage/blob) is a great solution for storing [blobs](https://developer.mozilla.org/docs/Web/API/Blob) that need to be frequently read. Here are some examples suitable for Vercel Blob:

*   Files that are programmatically uploaded or generated at build time, for display and download such as avatars, screenshots, cover images and videos
*   Large files such as videos and audios to take advantage of the global network
*   Files that you would normally store in an external file storage solution like Amazon S3. With your project hosted on Vercel, you can readily access and manage these files with Vercel Blob

Stored files are referred to as "blobs" once they're in the storage system, following cloud storage terminology.

## [Getting started](#getting-started)

```
import { put } from '@vercel/blob';
 
const blob = await put('avatar.jpg', imageFile, {
  access: 'public',
});
```

You can create and manage your Vercel Blob stores from your [account dashboard](/dashboard). You can scope your Vercel Blob stores to your Hobby team or [team](/docs/accounts/create-a-team), and connect them to as many projects as you want.

To get started, see the [server-side](/docs/storage/vercel-blob/server-upload), or [client-side](/docs/storage/vercel-blob/client-upload) quickstart guides. Or visit the full API reference for the [Vercel Blob SDK](/docs/storage/vercel-blob/using-blob-sdk).

## [Using Vercel Blob in your workflow](#using-vercel-blob-in-your-workflow)

If you'd like to know whether or not Vercel Blob can be integrated into your workflow, it's worth knowing the following:

*   You can have one or more Vercel Blob stores per Vercel account
*   You can use multiple Vercel Blob stores in one Vercel project
*   Each Vercel Blob store can be accessed by multiple Vercel projects Vercel Blob URLs are publicly accessible, but you can make them [unguessable](#security).
*   To add to or remove from the content of a Blob store, a valid [token](/docs/storage/vercel-blob/using-blob-sdk#read-write-token) is required

### [Transferring to another project](#transferring-to-another-project)

If you need to transfer your blob store from one project to another project in the same or different team, review [Transferring your store](/docs/storage#transferring-your-store).

## [Viewing and downloading blobs](#viewing-and-downloading-blobs)

Each Blob is served with a `content-disposition` header. Based on the MIME type of the uploaded blob, it is either set to `attachment` (force file download) or `inline` (can render in a browser tab). This is done to prevent hosting specific files on `@vercel/blob` like HTML web pages. Your browser will automatically download the blob instead of displaying it for these cases.

Currently `text/plain`, `text/xml`, `application/json`, `application/pdf`, `image/*`, `audio/*` and `video/*` resolve to a `content-disposition: inline` header.

All other MIME types default to `content-disposition: attachment`.

If you need a blob URL that always forces a download you can use the `downloadUrl` property on the blob object. This URL always has the `content-disposition: attachment` header no matter its MIME type.

```
import { list } from '@vercel/blob';
 
export default async function Page() {
  const response = await list();
 
  return (
    <>
      {response.blobs.map((blob) => (
        <a key={blob.pathname} href={blob.downloadUrl}>
          {blob.pathname}
        </a>
      ))}
    </>
  );
}
```

Alternatively the SDK exposes a helper function called `getDownloadUrl` that returns the same URL.

## [Caching](#caching)

When you request a blob URL using a browser, the content is cached in two places:

1.  Your browser's cache
2.  Vercel's [edge cache](/docs/edge-network/caching)

Both caches store blobs for up to 1 month by default to ensure optimal performance when serving content. While both systems aim to respect this duration, blobs may occasionally expire earlier.

### [Configuring cache duration](#configuring-cache-duration)

You can customize the caching duration using the `cacheControlMaxAge` option in the [`put()`](/docs/storage/vercel-blob/using-blob-sdk#put) and [`handleUpload`](/docs/storage/vercel-blob/using-blob-sdk#handleupload) methods.

The minimum configurable value is 60 seconds (1 minute). This represents the maximum time needed for our cache to update content behind a blob URL. For applications requiring faster updates, consider using a [Vercel function](/docs/functions) instead.

### [Important considerations when updating blobs](#important-considerations-when-updating-blobs)

When you delete or update (overwrite) a blob, the changes may take up to 60 seconds to propagate through our edge cache. However, browser caching presents additional challenges:

*   While our edge cache can update to serve the latest content, browsers will continue serving the cached version
*   To force browsers to fetch the updated content, add a unique query parameter to the blob URL:

```
<img
  src="https://1sxstfwepd7zn41q.public.blob.vercel-storage.com/blob-oYnXSVczoLa9yBYMFJOSNdaiiervF5.png?v=123456"
/>
```

For more information about updating existing blobs, see the [Overwriting blobs](#overwriting-blobs) section.

### [Best practice: Treat blobs as immutable](#best-practice:-treat-blobs-as-immutable)

For optimal performance and to avoid caching issues, consider treating blobs as immutable objects:

*   Instead of updating existing blobs, create new ones with different pathnames (or use `addRandomSuffix: true` option)
*   This approach avoids unexpected behaviors like outdated content appearing in your application

There are still valid use cases for mutable blobs with shorter cache durations, such as a single JSON file that's updated every 5 minutes with a top list of sales or other regularly refreshed data. For these scenarios, set an appropriate `cacheControlMaxAge` value and be mindful of caching behaviors.

## [Overwriting blobs](#overwriting-blobs)

By default, Vercel Blob prevents you from accidentally overwriting existing blobs by using the same pathname twice. When you attempt to upload a blob with a pathname that already exists, the operation will throw an error.

### [Using `allowOverwrite`](#using-allowoverwrite)

To explicitly allow overwriting existing blobs, you can use the `allowOverwrite` option:

```
const blob = await put('user-profile.jpg', imageFile, {
  access: 'public',
  allowOverwrite: true, // Enable overwriting an existing blob with the same pathname
});
```

This option is available in these methods:

*   `put()`
*   In client uploads via the `onBeforeGenerateToken()` function

### [When to use overwriting](#when-to-use-overwriting)

Overwriting blobs can be appropriate for certain use cases:

1.  Regularly updated files: For files that need to maintain the same URL but contain updated content (like JSON data files or configuration files)
2.  Content with predictable update patterns: For data that changes on a schedule and where consumers expect updates at the same URL

When overwriting blobs, be aware that due to [caching](#caching), changes won't be immediately visible. The minimum time for changes to propagate is 60 seconds, and browser caches may need to be explicitly refreshed.

### [Alternatives to overwriting](#alternatives-to-overwriting)

If you want to avoid overwriting existing content (recommended for most use cases), you have two options:

1.  Use `addRandomSuffix: true`: This automatically adds a unique random suffix to your pathnames:

```
const blob = await put('avatar.jpg', imageFile, {
  access: 'public',
  addRandomSuffix: true, // Creates a pathname like 'avatar-oYnXSVczoLa9yBYMFJOSNdaiiervF5.jpg'
});
```

2.  Generate unique pathnames programmatically: Create unique pathnames by adding timestamps, UUIDs, or other identifiers:

```
const timestamp = Date.now();
const blob = await put(`user-profile-${timestamp}.jpg`, imageFile, {
  access: 'public',
});
```

## [Blob Data Transfer](#blob-data-transfer)

Vercel Blob delivers content through a specialized network optimized for static assets:

*   Region-based distribution: Content is served from 18 regional hubs strategically located around the world
*   Optimized for non-critical assets: Well-suited for content "below the fold" that isn't essential for initial page rendering metrics like First Contentful Paint (FCP) or Largest Contentful Paint (LCP)
*   Cost-optimized for large assets: 3x more cost-efficient than [Fast Data Transfer](/docs/edge-network) on average
*   Great for media delivery: Ideal for large media files like images, videos, and documents

While [Fast Data Transfer](/docs/edge-network) provides city-level, ultra-low latency, Blob Data Transfer prioritizes cost-efficiency for larger assets where ultra-low latency isn't essential.

Blob Data Transfer fees apply only to downloads (outbound traffic), not uploads. See [pricing documentation](/docs/vercel-blob/usage-and-pricing) for details.

## [Upload charges](#upload-charges)

Upload charges depend on your implementation method:

*   [Client Uploads](/docs/vercel-blob/client-upload): No data transfer charges for uploads
*   [Server Uploads](/docs/vercel-blob/server-upload): [Fast Data Transfer](/docs/edge-network/manage-usage#fast-data-transfer) transfer charges apply when your Vercel application receives the file

## [SEO and search engine indexing](#seo-and-search-engine-indexing)

### [Search engine visibility of blobs](#search-engine-visibility-of-blobs)

While Vercel Blob URLs can be designed to be unique and unguessable (when using `addRandomSuffix: true`), they can still be indexed by search engines under certain conditions:

*   If you link to blob URLs from public webpages
*   If you embed blob content (images, PDFs, etc.) in indexed content
*   If you share blob URLs publicly, even in contexts outside your application

By default, Vercel Blob does not provide a `robots.txt` file or other indexing controls. This means search engines like Google may discover and index your blob content if they find links to it.

### [Preventing search engine indexing](#preventing-search-engine-indexing)

If you want to prevent search engines from indexing your blob content, you need to upload a `robots.txt` file directly to your blob store:

1.  Go to your [Storage page](https://vercel.com/d?to=%2F%5Bteam%5D%2F~%2Fstores&title=Go+to+Storage) and select your blob store
2.  Upload a `robots.txt` file to the root of your blob store with appropriate directives

Example `robots.txt` content to block all crawling of your blob store:

`User-agent: * Disallow: /`

### [Removing already indexed blob content](#removing-already-indexed-blob-content)

If your blob content has already been indexed by search engines:

1.  Verify your website ownership in [Google Search Console](https://search.google.com/search-console/)
2.  Upload a `robots.txt` file to your blob store as described above
3.  Use the "Remove URLs" tool in Google Search Console to request removal

## [Blob regions](#blob-regions)

Currently, Vercel Blob physically stores all data in a single Vercel region: `iad1` (`us-east-1`) in the United States. While this setup ensures high performance and reliability, it may not meet your data residency requirements.

Make sure to check your data residency requirements before using Vercel Blob. If your application requires storing data in a specific region or country, Vercel Blob may not be suitable at this time.

## [Simple operations](#simple-operations)

Simple operations in Vercel Blob are specific read actions counted for billing purposes:

*   When the [`head()`](/docs/vercel-blob/using-blob-sdk#head) method is called to retrieve blob metadata
*   When a blob is accessed by its URL and it's a cache MISS

A cache MISS occurs when the blob is accessed for the first time or when its previously cached version has expired. Note that blob URL access resulting in a cache HIT does not count as a Simple Operation.

## [Advanced operations](#advanced-operations)

Advanced operations in Vercel Blob are write, copy, and listing actions counted for billing purposes:

*   When the [`put()`](/docs/vercel-blob/using-blob-sdk#put) method is called to upload a blob
*   When the [`upload()`](/docs/vercel-blob/using-blob-sdk#upload) method is used for client-side uploads
*   When the [`copy()`](/docs/vercel-blob/using-blob-sdk#copy) method is called to copy an existing blob
*   When the [`list()`](/docs/vercel-blob/using-blob-sdk#list) method is called to list blobs in your store

For [multipart uploads](#multipart-uploads), multiple advanced operations are counted:

*   One operation when starting the upload
*   One operation for each part uploaded
*   One operation for completing the upload

Delete operations using the [`del()`](/docs/vercel-blob/using-blob-sdk#del) are free of charge. They are considered advanced operations for [operation rate limits](#operation-rate-limits) but not for billing.

## [Storage calculation](#storage-calculation)

Vercel Blob measures your storage usage by taking snapshots of your blob store size every 15 minutes and averages these measurements over the entire month to calculate your GB-month usage. This approach accounts for fluctuations in storage as blobs are added and removed, ensuring you're only billed for your actual usage over time, not peak usage.

The Vercel dashboard displays two metrics:

*   Latest value: The most recent measurement of your blob store size
*   Monthly average: The average of all measurements throughout the billing period (this is what you're billed for)

Example:

1.  Day 1: Upload a 2GB file → Store size: 2GB
2.  Day 15: Add 1GB file → Store size: 3GB
3.  Day 25: Delete 2GB file → Store size: 1GB

Month end billing:

*   Latest value: 1GB
*   Monthly average: ~2GB (billed amount)

If no changes occur in the following month (no new uploads or deletions), each 15-minute measurement would consistently show 1 GB. In this case, your next month's billing would be exactly 1 GB/month, as your monthly average would equal your latest value.

## [Multipart uploads](#multipart-uploads)

Vercel Blob supports [multipart uploads](/docs/vercel-blob/using-blob-sdk#multipart-uploads) for large files, which provides significant advantages when transferring substantial amounts of data.

Multipart uploads work by splitting large files into smaller chunks (parts) that are uploaded independently and then reassembled on the server. This approach offers several key benefits:

*   Improved upload reliability: If a network issue occurs during upload, only the affected part needs to be retried instead of restarting the entire upload
*   Better performance: Multiple parts can be uploaded in parallel, significantly increasing transfer speed
*   Progress tracking: More granular upload progress reporting as each part completes

We recommend using multipart uploads for files larger than 100 MB. Both the [`put()`](/docs/vercel-blob/using-blob-sdk#put) and [`upload()`](/docs/vercel-blob/using-blob-sdk#upload) methods handle all the complexity of splitting, uploading, and reassembling the file for you.

For billing purposes, multipart uploads count as multiple advanced operations:

*   One operation when starting the upload
*   One operation for each part uploaded
*   One operation for completing the upload

This approach ensures reliable handling of large files while maintaining the performance and efficiency expected from modern cloud storage solutions.

## [Durability and availability](#durability-and-availability)

Vercel Blob leverages [Amazon S3](https://aws.amazon.com/s3/) as its underlying storage infrastructure, providing industry-leading durability and availability:

*   Durability: Vercel Blob offers 99.999999999% (11 nines) durability. This means that even with one billion objects, you could expect to go a hundred years without losing a single one.
*   Availability: Vercel Blob provides 99.99% (4 nines) availability in a given year, ensuring that your data is accessible when you need it.

These guarantees are backed by [S3's robust architecture](https://docs.aws.amazon.com/AmazonS3/latest/userguide/DataDurability.html), which includes automatic replication and error correction mechanisms.

## [Folders and slashes](#folders-and-slashes)

Vercel Blob has folders support to organize your blobs:

```
const blob = await put('folder/file.txt', 'Hello World!', { access: 'public' });
```

The path `folder/file.txt` creates a folder named `folder` and a blob named `file.txt`. To list all blobs within a folder, use the [`list`](/docs/storage/vercel-blob/using-blob-sdk#list-blobs) function:

```
const listOfBlobs = await list({
  cursor,
  limit: 1000,
  prefix: 'folder/',
});
```

You don't need to create folders. Upload a file with a path containing a slash `/`, and Vercel Blob will interpret the slashes as folder delimiters.

In the Vercel Blob file browser on the Vercel dashboard, any pathname with a slash `/` is treated as a folder. However, these are not actual folders like in a traditional file system; they are used for organizing blobs in listings and the file browser.

## [More resources](#more-resources)

*   [Client Upload Quickstart](/docs/storage/vercel-blob/client-upload)
*   [Server Upload Quickstart](/docs/storage/vercel-blob/server-upload)
*   [Vercel Blob SDK](/docs/storage/vercel-blob/using-blob-sdk)
*   [Vercel Blob Pricing](/docs/vercel-blob/usage-and-pricing)
*   [Vercel Blob Security](/docs/storage/vercel-blob/security)
*   [Vercel Blob Examples](/docs/storage/vercel-blob/examples)
*   [Observability](/docs/observability)

Last updated on March 12, 2025


___


# Server Uploads with Vercel Blob

Vercel Blob is available on [all plans](/docs/plans)

Those with the [owner, member, developer](/docs/rbac/access-roles#owner, member, developer-role) role can access this feature

In this guide, you'll learn how to do the following:

*   Use the Vercel dashboard to create a Blob store connected to a project
*   Upload a file using the Blob SDK from the server

Vercel has a [4.5 MB request body size limit](/docs/functions/serverless-functions/runtimes#request-body-size) on Vercel Functions. If you need to upload larger files, use [client uploads](/docs/storage/vercel-blob/client-upload).

## [Prerequisites](#prerequisites)

Vercel Blob works with any frontend framework. First, install the package:

pnpmyarnnpmbun

```
npm i @vercel/blob
```

1.  ### [Create a Blob store](#create-a-blob-store)
    
    Navigate to the [Project](/docs/projects/overview) you'd like to add the blob store to. Select the Storage tab, then select the Connect Database button.
    
    Under the Create New tab, select Blob and then the Continue button.
    
    Use the name "Images" and select Create a new Blob store. Select the environments where you would like the read-write token to be included. You can also update the prefix of the Environment Variable in Advanced Options
    
    Once created, you are taken to the Vercel Blob store page.
    
2.  ### [Prepare your local project](#prepare-your-local-project)
    
    Since you created the Blob store in a project, we automatically created and added the following Environment Variable to the project for you.
    
    *   `BLOB_READ_WRITE_TOKEN`
    
    To use this Environment Variable locally, we recommend pulling it with the Vercel CLI:
    
    ```
    vercel env pull
    ```
    

Server uploads are perfectly fine as long as you do not need to upload files larger than [4.5 MB on Vercel](/docs/functions/serverless-functions/runtimes#request-body-size). If you need to upload larger files, consider using [client uploads](/docs/storage/vercel-blob/client-upload).

## [Upload a file using Server Actions](#upload-a-file-using-server-actions)

The following example shows how to use a [Server Action](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations) with Next.js App Router to upload a file to Vercel Blob.

```
import { put } from '@vercel/blob';
import { revalidatePath } from 'next/cache';
 
export async function Form() {
  async function uploadImage(formData: FormData) {
    'use server';
    const imageFile = formData.get('image') as File;
    const blob = await put(imageFile.name, imageFile, {
      access: 'public',
      addRandomSuffix: true,
    });
    revalidatePath('/');
    return blob;
  }
 
  return (
    <form action={uploadImage}>
      <label htmlFor="image">Image</label>
      <input type="file" id="image" name="image" required />
      <button>Upload</button>
    </form>
  );
}
```

Then, add the hostname to your `next.config.js` file including the store id from the dashboard:

```
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'my-blob-store.public.blob.vercel-storage.com',
        port: '',
      },
    ],
  },
};
 
module.exports = nextConfig;
```

This will allow you to use [`next/image`](https://nextjs.org/docs/app/api-reference/components/image) to display images from your Vercel Blob store.

```
import { list } from '@vercel/blob';
import Image from 'next/image';
 
export async function Images() {
  async function allImages() {
    const blobs = await list();
    return blobs;
  }
  const images = await allImages();
 
  return (
    <section>
      {images.blobs.map((image) => (
        <Image
          priority
          key={image.pathname}
          src={image.url}
          alt="Image"
          width={200}
          height={200}
        />
      ))}
    </section>
  );
}
```

Read more about [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations) and [App Router](https://nextjs.org/docs) on the Next.js documentation.

## [Upload a file using a server upload page and route](#upload-a-file-using-a-server-upload-page-and-route)

You can upload files to Vercel Blob using Route Handlers/API Routes. The following example shows how to upload a file to Vercel Blob using a server upload page and route.

1.  ### [Create a server upload page](#create-a-server-upload-page)
    
    This page will upload files to your server. The files will then be sent to Vercel Blob.
    
    Next.js (/app)Next.js (/pages)
    
    ```
    'use client';
     
    import type { PutBlobResult } from '@vercel/blob';
    import { useState, useRef } from 'react';
     
    export default function AvatarUploadPage() {
      const inputFileRef = useRef<HTMLInputElement>(null);
      const [blob, setBlob] = useState<PutBlobResult | null>(null);
      return (
        <>
          <h1>Upload Your Avatar</h1>
     
          <form
            onSubmit={async (event) => {
              event.preventDefault();
     
              if (!inputFileRef.current?.files) {
                throw new Error('No file selected');
              }
     
              const file = inputFileRef.current.files[0];
     
              const response = await fetch(
                `/api/avatar/upload?filename=${file.name}`,
                {
                  method: 'POST',
                  body: file,
                },
              );
     
              const newBlob = (await response.json()) as PutBlobResult;
     
              setBlob(newBlob);
            }}
          >
            <input name="file" ref={inputFileRef} type="file" required />
            <button type="submit">Upload</button>
          </form>
          {blob && (
            <div>
              Blob url: <a href={blob.url}>{blob.url}</a>
            </div>
          )}
        </>
      );
    }
    ```
    
2.  ### [Create a server upload route](#create-a-server-upload-route)
    
    This route forwards the file to Vercel Blob and returns the URL of the uploaded file to the browser.
    
    Next.js (/app)Next.js (/pages)
    
    ```
    import { put } from '@vercel/blob';
    import { NextResponse } from 'next/server';
     
    export async function POST(request: Request): Promise<NextResponse> {
      const { searchParams } = new URL(request.url);
      const filename = searchParams.get('filename');
     
      const blob = await put(filename, request.body, {
        access: 'public',
        addRandomSuffix: true,
      });
     
      return NextResponse.json(blob);
    }
    ```
    

### [Testing your page](#testing-your-page)

1.  ### [Run your application locally](#run-your-application-locally)
    
    Run your application locally and visit `/avatar/upload` to upload the file to your store. The browser will display the unique URL created for the file.
    
    When your local website is served on `http://localhost:3000`, then the `onUploadCompleted` step won't succeed as Vercel Blob cannot contact your localhost. Instead, we recommend you run your local application through a tunneling service like [ngrok](https://ngrok.com/), so you can experience the full Vercel Blob development flow locally.
    
2.  ### [Review the Blob object metadata](#review-the-blob-object-metadata)
    
    *   Go to the Vercel Project where you created the store
    *   Select the Storage tab and select your new store
    *   Paste the blob object URL returned in the previous step in the Blob URL input box in the Browser section and select Lookup
    *   The following blob object metadata will be displayed: file name, path, size, uploaded date, content type and HTTP headers
    *   You also have the option to download and delete the file from this page

You have successfully uploaded an object to your Vercel Blob store and are able to review it's metadata, download, and delete it from your Vercel Storage Dashboard.

## [Next steps](#next-steps)

*   Learn how to [use the methods](/docs/storage/vercel-blob/using-blob-sdk) available with the `@vercel/blob` package

Last updated on March 4, 2025