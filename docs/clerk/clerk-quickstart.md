Install @clerk/nextjs
Run the following command to install the SDK:

npm
yarn
pnpm
terminal

npm install @clerk/nextjs
2
Set your Clerk API keys
Add these keys to your .env or create the file if it doesn't exist. Retrieve these keys anytime from the API keys page.

.env


NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_bWF4aW11bS1tb3NxdWl0by0zOS5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_FdlUxPkvwamiScKzLmqhdezES9OAh4fJH8GQjhGb7y
3
Update middleware.ts

Update your middleware file, or create one at the root of your project, or the src/ directory if you're using a src/ directory structure.

The clerkMiddleware helper enables authentication and is where you'll configure your protected routes.

middleware.ts

import { clerkMiddleware } from "@clerk/nextjs/server";

export default clerkMiddleware();

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
4
Add ClerkProvider to your app

The ClerkProvidercomponent provides Clerk's authentication context to your app. It's recommended to wrap your entire app at the entry point with ClerkProvider to make authentication globally accessible. See the reference docs for other configuration options.

Copy and paste the following file into your layout.tsx file. This creates a header with Clerk's prebuilt components to allow users to sign in and out.

App Router
Pages Router
/src/app/layout.tsx

import { type Metadata } from 'next'
import {
  ClerkProvider,
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import { Geist, Geist_Mono } from 'next/font/google'
import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Clerk Next.js Quickstart',
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
          <header className="flex justify-end items-center p-4 gap-4 h-16">
            <SignedOut>
              <SignInButton />
              <SignUpButton />
            </SignedOut>
            <SignedIn>
              <UserButton />
            </SignedIn>
          </header>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
Create your first user
Run your project. Then, visit your app's homepage at http://localhost:3000 and sign up to create your first user.

npm
yarn
pnpm
terminal

npm run dev
Next steps

Utilize your own pages for authentication
The Account Portal is the fastest way to add authentication, but Clerk has pre-built, customizable components to use in your app too.


___


Build your own sign-in-or-up page for your Next.js app with Clerk
This guide shows you how to use the <SignIn /> component to build a custom page that allows users to sign in or sign up within a single flow.

To set up separate sign-in and sign-up pages, follow this guide, and then follow the custom sign-up page guide.

Note

Just getting started with Clerk and Next.js? See the quickstart tutorial!

Build a sign-in-or-up page
The following example demonstrates how to render the <SignIn /> component on a dedicated page using the Next.js optional catch-all route.

app/sign-in/[[...sign-in]]/page.tsx

import { SignIn } from '@clerk/nextjs'

export default function Page() {
  return <SignIn />
}
Make the sign-in-or-up route public
By default, clerkMiddleware() makes all routes public. This step is specifically for applications that have configured clerkMiddleware() to make all routes protected. If you have not configured clerkMiddleware() to protect all routes, you can skip this step.

To make the sign-in route public:

Navigate to your middleware.ts file.
Create a new route matcher that matches the sign-in route, or you can add it to your existing route matcher that is making routes public.
Create a check to see if the user's current route is a public route. If it is not a public route, use auth.protect() to protect the route.
middleware.ts

import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'

const isPublicRoute = createRouteMatcher(['/sign-in(.*)'])

export default clerkMiddleware(async (auth, req) => {
  if (!isPublicRoute(req)) {
    await auth.protect()
  }
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
Update your environment variables
Set the CLERK_SIGN_IN_URL environment variable to tell Clerk where the <SignIn /> component is being hosted.
Set CLERK_SIGN_IN_FALLBACK_REDIRECT_URL as a fallback URL incase users visit the /sign-in route directly.
Set CLERK_SIGN_UP_FALLBACK_REDIRECT_URL as a fallback URL incase users select the 'Don't have an account? Sign up' link at the bottom of the component.
Learn more about these environment variables and how to customize Clerk's redirect behavior in the dedicated guide.

.env

NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/
NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/
Visit your new page
Run your project with the following command:

npm
yarn
pnpm
bun
terminal

npm run dev
Visit your new custom page locally at localhost:3000/sign-in.


___


Read session and user data in your Next.js app with Clerk
Clerk provides a set of hooks and helpers that you can use to access the active session and user data in your Next.js application. Here are examples of how to use these helpers in both the client and server-side to get you started.

Server-side
App Router
auth() and currentUser() are App Router-specific helpers that you can use inside of your Route Handlers, Middleware, Server Components, and Server Actions.

The auth() helper will return the Auth object of the currently active user.
The currentUser() helper will return the Backend User object of the currently active user. This is helpful if you want to render information, like their first and last name, directly from the server. Under the hood, currentUser() uses the clerkClient wrapper to make a call to the Backend API. This does count towards the Backend API request rate limit. This also uses fetch() so it is automatically deduped per request.
The following example uses the auth() helper to validate an authenticated user and the currentUser() helper to access the Backend User object for the authenticated user.

Note

Any requests from a Client Component to a Route Handler will read the session from cookies and will not need the token sent as a Bearer token.

Server components and actions
Route Handler
app/page.tsx

import { auth, currentUser } from '@clerk/nextjs/server'

export default async function Page() {
  // Get the userId from auth() -- if null, the user is not signed in
  const { userId } = await auth()

  // Protect the route by checking if the user is signed in
  if (!userId) {
    return <div>Sign in to view this page</div>
  }

  // Get the Backend API User object when you need access to the user's information
  const user = await currentUser()

  // Use `user` to render user details or create UI elements
  return <div>Welcome, {user.firstName}!</div>
}
Pages Router
For Next.js applications using the Pages Router, the getAuth() helper will return the Auth object of the currently active user, which contains important information like the current user's session ID, user ID, and organization ID. The userId can be used to protect your API routes.

In some cases, you may need the full Backend User object of the currently active user. This is helpful if you want to render information, like their first and last name, directly from the server.

The clerkClient() helper returns an instance of the JavaScript Backend SDK, which exposes Clerk's Backend API resources through methods such as the getUser() method. This method returns the full Backend User object.

In the following example, the userId is passed to the Backend SDK's getUser() method to get the user's full Backend User object.

API Route
getServerSideProps
pages/api/auth.ts

import { getAuth, clerkClient } from '@clerk/nextjs/server'
import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Use `getAuth()` to get the user's ID
  const { userId } = getAuth(req)

  // Protect the route by checking if the user is signed in
  if (!userId) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Initialize the Backend SDK
  const client = await clerkClient()

  // Get the user's full `Backend User` object
  const user = await client.users.getUser(userId)

  return res.status(200).json({ user })
}
Client-side
useAuth()
The following example uses the useAuth() hook to access the current auth state, as well as helper methods to manage the current active session. The hook returns userId, which can be used to protect your routes.

example.tsx

export default function Example() {
  const { isLoaded, isSignedIn, userId, sessionId, getToken } = useAuth()

  if (!isLoaded) {
    return <div>Loading...</div>
  }

  if (!isSignedIn) {
    // You could also add a redirect to the sign-in page here
    return <div>Sign in to view this page</div>
  }

  return (
    <div>
      Hello, {userId}! Your current active session is {sessionId}.
    </div>
  )
}
useUser()
The following example uses the useUser() hook to access the User object, which contains the current user's data such as their full name. The isLoaded and isSignedIn properties are used to handle the loading state and to check if the user is signed in, respectively.

src/Example.tsx

export default function Example() {
  const { isSignedIn, user, isLoaded } = useUser()

  if (!isLoaded) {
    return <div>Loading...</div>
  }

  if (!isSignedIn) {
    return <div>Sign in to view this page</div>
  }

  return <div>Hello {user.firstName}!</div>
}
Feedback


